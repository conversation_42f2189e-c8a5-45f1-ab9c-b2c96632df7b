<script setup>
import { useMessageStore } from '@/Store/useMessageStore';
import { useIntersectionObserver } from '@vueuse/core';
import { ref } from 'vue';

const messageStore = useMessageStore();

const props = defineProps({
    room: { type: Object, required: true },
})

const target = ref(null)

const { isActive, pause, resume } = useIntersectionObserver(
    target,
    ([{ isIntersecting }]) => {
        if (isIntersecting && messageStore.getLoaded) {
            messageStore.fetchMoreMessages(props.room.slug);
        }
    },
)
</script>
<template>

    <main id="page-content" class="absolute inset-0">
        <div
            class="container mx-auto space-y-6 px-4 py-24 lg:p-8 lg:pb-28 xl:max-w-7xl flex flex-col h-full overflow-y-auto">
            <!-- Messages Received -->
            <div ref="target"></div>

            <div v-for="message in messageStore.allmessages" :key="message.id"
                class="flex w-5/6 flex-col gap-2 lg:w-2/3 xl:w-1/3" :class="{
                    'items-start': message.user.id !== $page.props.auth.user.id,
                    'items-end ms-auto': message.user.id === $page.props.auth.user.id
                }">
                <p v-if="message.user.id != $page.props.auth.user.id" class="text-sm font-medium text-slate-500">
                    {{ message.user.name }}
                </p>
                <div class="rounded-2xl px-5 py-3" :class="{
                    'bg-indigo-600 text-white rounded-tl-none': message.user.id === $page.props.auth.user.id,
                    'bg-gray-100 text-slate-600 rounded-br-none': message.user.id !== $page.props.auth.user.id
                }">
                    <p class="font-semibold">
                        {{ message.content }}
                    </p>
                </div>
                <p class="text-xs font-medium text-slate-500 text-right">
                    {{ message.created_at }}
                </p>
            </div>
        </div>
    </main>
</template>