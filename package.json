{"private": true, "type": "module", "scripts": {"build": "vite build && vite build --ssr", "dev": "vite"}, "devDependencies": {"@inertiajs/vue3": "^1.0.0", "@tailwindcss/forms": "^0.5.3", "@vitejs/plugin-vue": "^5.0.0", "@vue/server-renderer": "^3.4.0", "autoprefixer": "^10.4.12", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-echo": "^1.17.0", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.31", "pusher-js": "^8.4.0-rc2", "tailwindcss": "^3.2.1", "vite": "^5.0", "vue": "^3.4.0"}, "dependencies": {"@vueuse/core": "^11.3.0", "pinia": "^2.2.6", "ws": "^8.18.0"}}