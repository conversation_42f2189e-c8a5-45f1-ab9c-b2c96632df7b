<?php

namespace App\Exports;

use App\Models\Product;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class ProductsExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request ?? request();
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Product::query();

        // Apply search filter
        if ($this->request->filled('search')) {
            $search = $this->request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('category', 'like', "%{$search}%");
            });
        }

        // Apply category filter
        if ($this->request->filled('category')) {
            $query->where('category', $this->request->input('category'));
        }

        // Apply status filter
        if ($this->request->filled('status')) {
            $query->where('status', $this->request->input('status'));
        }

        // Apply price range filter
        if ($this->request->filled('price_min')) {
            $query->where('price', '>=', $this->request->input('price_min'));
        }
        if ($this->request->filled('price_max')) {
            $query->where('price', '<=', $this->request->input('price_max'));
        }

        // Apply date range filter
        if ($this->request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $this->request->input('date_from'));
        }
        if ($this->request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $this->request->input('date_to'));
        }

        // Apply sorting
        $sortField = $this->request->input('sort', 'id');
        $sortDirection = $this->request->input('direction', 'asc');
        $query->orderBy($sortField, $sortDirection);

        return $query->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Category',
            'Description',
            'Price',
            'Stock',
            'Status',
            'Created Date',
        ];
    }

    /**
     * @param mixed $row
     *
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->name,
            $row->category,
            $row->description,
            $row->price,
            $row->stock,
            ucfirst(str_replace('_', ' ', $row->status)),
            $row->created_at->format('Y-m-d'),
        ];
    }

    /**
     * @param Worksheet $sheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
        ];
    }
}
