<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Custom Logic & Data Formats - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="custom-logic.html">Custom Logic</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Custom Logic & Data Formats</h1>
            <p class="lead">Learn how to adapt the Dynamic Table component to your specific data structures and business logic.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The Dynamic Table component is designed to be flexible and adaptable to different data formats and business requirements.
            </div>
            
            <div class="section-card">
                <h2>Customizing the Query Builder</h2>
                <p>The <code>HasDataTable</code> trait uses a query builder to fetch data. You can override the <code>buildTableQuery</code> method to implement your own custom logic:</p>
                <pre><code>protected function buildTableQuery(Request $request)
{
    // Start with a base query
    $query = $this->getBaseQuery();
    
    // Apply your custom logic
    if ($request->has('custom_filter')) {
        $customValue = $request->input('custom_filter');
        
        // Example: Complex join with related tables
        $query->join('related_table', 'main_table.id', '=', 'related_table.main_id')
              ->where('related_table.some_field', $customValue);
    }
    
    // Apply date range filtering with custom format
    if ($request->has('date_range')) {
        $dateRange = explode(' - ', $request->input('date_range'));
        if (count($dateRange) == 2) {
            $startDate = Carbon::createFromFormat('m/d/Y', $dateRange[0])->startOfDay();
            $endDate = Carbon::createFromFormat('m/d/Y', $dateRange[1])->endOfDay();
            
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
    }
    
    // Apply standard filters after your custom logic
    $this->applyFilters($query, $request);
    
    // Apply sorting
    $this->applySorting($query, $request);
    
    return $query;
}</code></pre>
                <p>By overriding this method, you can implement any custom query logic while still leveraging the standard filtering and sorting functionality.</p>
            </div>
            
            <div class="section-card">
                <h2>Working with Non-Standard Data Formats</h2>
                <p>If your data doesn't follow the standard Eloquent model format, you can adapt the component in several ways:</p>
                
                <h3>1. Custom Data Source</h3>
                <p>Override the <code>getBaseQuery</code> method to use a different data source:</p>
                <pre><code>protected function getBaseQuery()
{
    // Example: Use a different model or repository
    return app(CustomRepository::class)->getQuery();
    
    // Example: Use a raw query
    return DB::table('custom_view')
        ->select('id', 'custom_field1', 'custom_field2');
        
    // Example: Use an API data source
    // For API data sources, you'll need to handle pagination manually
    // This is just a starting point
    $apiData = Http::get('https://api.example.com/data')->json();
    return collect($apiData);
}</code></pre>
                
                <h3>2. Custom Data Transformation</h3>
                <p>If you need to transform the data before displaying it, you can override the <code>handleDataTable</code> method:</p>
                <pre><code>public function handleDataTable(Request $request, $viewPath, $extraData = [])
{
    // Build the query
    $query = $this->buildTableQuery($request);
    
    // Get the items with pagination
    $perPage = $request->input('per_page', 10);
    $items = $query->paginate($perPage);
    
    // Transform the data
    $transformedItems = $items->getCollection()->map(function ($item) {
        return [
            'id' => $item->id,
            'custom_name' => $item->first_name . ' ' . $item->last_name,
            'formatted_date' => Carbon::parse($item->created_at)->format('F j, Y'),
            'status_text' => $this->getStatusText($item->status_code),
            'calculated_value' => $this->calculateValue($item),
            // Add any other custom transformations
        ];
    });
    
    // Replace the items collection with the transformed collection
    $items->setCollection($transformedItems);
    
    // Continue with the standard response handling
    // ...
}</code></pre>
                
                <h3>3. JSON or API Data</h3>
                <p>If your data comes from an API or is in JSON format, you can adapt the component to handle it:</p>
                <pre><code>public function apiDataTable(Request $request)
{
    // Fetch data from API
    $response = Http::get('https://api.example.com/data', [
        'page' => $request->input('page', 1),
        'per_page' => $request->input('per_page', 10),
        'search' => $request->input('search'),
        'sort' => $request->input('sort'),
        'direction' => $request->input('direction', 'asc'),
        // Add any other parameters
    ]);
    
    $data = $response->json();
    
    // Create a custom paginator
    $items = new \Illuminate\Pagination\LengthAwarePaginator(
        collect($data['items']),
        $data['total'],
        $data['per_page'],
        $data['current_page'],
        ['path' => $request->url()]
    );
    
    // Get filter options
    $filterOptions = $this->getApiFilterOptions();
    
    // Handle AJAX requests
    if ($request->ajax() || $request->wantsJson()) {
        return response()->json([
            'table' => view('components.data-table.table-rows', ['items' => $items])->render(),
            'pagination' => view('components.data-table.pagination', ['items' => $items])->render(),
            'success' => true,
            'message' => 'Data loaded successfully',
            'total' => $items->total(),
            'per_page' => $items->perPage(),
            'current_page' => $items->currentPage(),
            'last_page' => $items->lastPage(),
            'sort' => $request->input('sort'),
            'direction' => $request->input('direction', 'asc'),
        ]);
    }
    
    return view('your-view', [
        'items' => $items,
        'filterOptions' => $filterOptions,
    ]);
}</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Custom Filter Logic</h2>
                <p>If you need custom filter logic, you can override the <code>applyFilters</code> method:</p>
                <pre><code>protected function applyFilters($query, Request $request)
{
    // Apply standard filters first
    parent::applyFilters($query, $request);
    
    // Apply custom filters
    if ($request->has('complex_filter')) {
        $value = $request->input('complex_filter');
        
        // Example: Complex filter with subquery
        $query->whereExists(function ($subquery) use ($value) {
            $subquery->select(DB::raw(1))
                     ->from('related_table')
                     ->whereRaw('related_table.main_id = main_table.id')
                     ->where('related_table.value', '>', $value);
        });
    }
    
    // Example: Custom date range filter
    if ($request->has('custom_date_range')) {
        $range = $request->input('custom_date_range');
        
        switch ($range) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                break;
            case 'yesterday':
                $query->whereDate('created_at', Carbon::yesterday());
                break;
            case 'this_week':
                $query->whereBetween('created_at', [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()]);
                break;
            case 'last_week':
                $query->whereBetween('created_at', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->subWeek()->endOfWeek()]);
                break;
            case 'this_month':
                $query->whereMonth('created_at', Carbon::now()->month);
                break;
            case 'custom':
                // Handle custom date range from request
                break;
        }
    }
    
    return $query;
}</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Custom Column Rendering</h2>
                <p>For complex data display needs, you can use custom column renderers:</p>
                <pre><code>public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class;
    $this->tableColumns = [
        'id' => 'ID',
        'name' => 'Name',
        'complex_data' => 'Complex Data',
        'json_data' => 'JSON Data',
        'related_data' => 'Related Data',
        'calculated' => 'Calculated Value',
    ];
    
    // Custom rendering functions
    $this->columnRenderers = [
        // Render complex nested data
        'complex_data' => function($item) {
            if (!isset($item->complex_data) || empty($item->complex_data)) {
                return 'N/A';
            }
            
            $html = '<ul class="nested-data">';
            foreach ($item->complex_data as $key => $value) {
                $html .= '<li><strong>' . e($key) . ':</strong> ' . e($value) . '</li>';
            }
            $html .= '</ul>';
            
            return $html;
        },
        
        // Render JSON data
        'json_data' => function($item) {
            if (!isset($item->json_data)) {
                return 'N/A';
            }
            
            $data = is_string($item->json_data) ? json_decode($item->json_data) : $item->json_data;
            
            if (!$data) {
                return 'Invalid JSON';
            }
            
            return '<pre class="json-data">' . e(json_encode($data, JSON_PRETTY_PRINT)) . '</pre>';
        },
        
        // Render related data
        'related_data' => function($item) {
            if (!$item->relatedItems || $item->relatedItems->isEmpty()) {
                return 'No related items';
            }
            
            $html = '<div class="related-items">';
            foreach ($item->relatedItems as $related) {
                $html .= '<div class="related-item">';
                $html .= '<span class="related-name">' . e($related->name) . '</span>';
                $html .= '<span class="related-value">' . e($related->value) . '</span>';
                $html .= '</div>';
            }
            $html .= '</div>';
            
            return $html;
        },
        
        // Render calculated value
        'calculated' => function($item) {
            // Perform complex calculation
            $value = $this->performComplexCalculation($item);
            
            // Format based on value
            if ($value < 0) {
                return '<span class="text-danger">$' . number_format(abs($value), 2) . ' (Loss)</span>';
            } else {
                return '<span class="text-success">$' . number_format($value, 2) . ' (Profit)</span>';
            }
        },
    ];
}

// Example of a complex calculation method
protected function performComplexCalculation($item)
{
    // Your custom business logic here
    $revenue = $item->sales_amount;
    $costs = $item->material_cost + $item->labor_cost + $item->overhead;
    $taxes = $revenue * 0.21; // 21% tax rate
    
    return $revenue - $costs - $taxes;
}</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Custom Actions and Buttons</h2>
                <p>You can add custom action buttons to each row by modifying the table rows template:</p>
                <pre><code>@foreach($items as $item)
    &lt;tr&gt;
        @foreach($columns as $key => $column)
            &lt;td&gt;
                @if(isset($columnRenderers[$key]))
                    {!! $columnRenderers[$key]($item) !!}
                @else
                    {{ $item->{$key} }}
                @endif
            &lt;/td&gt;
        @endforeach
        &lt;td&gt;
            &lt;div class="action-buttons"&gt;
                &lt;!-- Standard actions --&gt;
                &lt;a href="{{ route('items.show', $item->id) }}" class="btn btn-sm btn-info" title="View"&gt;
                    &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
                &lt;/a&gt;
                
                &lt;!-- Custom actions based on item state --&gt;
                @if($item->status === 'pending')
                    &lt;button type="button" class="btn btn-sm btn-success approve-item" 
                            data-id="{{ $item->id }}" title="Approve"&gt;
                        &lt;i class="fas fa-check"&gt;&lt;/i&gt;
                    &lt;/button&gt;
                    
                    &lt;button type="button" class="btn btn-sm btn-danger reject-item" 
                            data-id="{{ $item->id }}" title="Reject"&gt;
                        &lt;i class="fas fa-times"&gt;&lt;/i&gt;
                    &lt;/button&gt;
                @endif
                
                @if($item->status === 'active')
                    &lt;button type="button" class="btn btn-sm btn-warning suspend-item" 
                            data-id="{{ $item->id }}" title="Suspend"&gt;
                        &lt;i class="fas fa-pause"&gt;&lt;/i&gt;
                    &lt;/button&gt;
                @endif
                
                @if($item->status === 'inactive')
                    &lt;button type="button" class="btn btn-sm btn-success activate-item" 
                            data-id="{{ $item->id }}" title="Activate"&gt;
                        &lt;i class="fas fa-play"&gt;&lt;/i&gt;
                    &lt;/button&gt;
                @endif
                
                &lt;!-- Custom action with confirmation --&gt;
                &lt;button type="button" class="btn btn-sm btn-danger delete-item" 
                        data-id="{{ $item->id }}" 
                        data-name="{{ $item->name }}"
                        data-confirm="Are you sure you want to delete {{ $item->name }}?"
                        title="Delete"&gt;
                    &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
                &lt;/button&gt;
            &lt;/div&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
@endforeach</code></pre>
                <p>Then add JavaScript to handle these custom actions:</p>
                <pre><code>// In your custom JavaScript file
$(document).ready(function() {
    // Handle approve action
    $(document).on('click', '.approve-item', function() {
        const id = $(this).data('id');
        
        // Show loading state
        $(this).html('&lt;i class="fas fa-spinner fa-spin"&gt;&lt;/i&gt;');
        
        // Send AJAX request
        $.ajax({
            url: `/items/${id}/approve`,
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                // Show success message
                toastr.success('Item approved successfully');
                
                // Refresh the table
                fetchTableData(window.location.href);
            },
            error: function(xhr) {
                // Show error message
                toastr.error('Error approving item');
                
                // Reset button
                $(this).html('&lt;i class="fas fa-check"&gt;&lt;/i&gt;');
            }
        });
    });
    
    // Similar handlers for other custom actions
    // ...
});</code></pre>
            </div>
            
            <div class="mt-4">
                <p>These examples demonstrate how to adapt the Dynamic Table component to your specific data formats and business logic. You can mix and match these approaches to create a highly customized table that meets your exact requirements.</p>
                <a href="examples.html" class="btn btn-primary">See More Examples</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
