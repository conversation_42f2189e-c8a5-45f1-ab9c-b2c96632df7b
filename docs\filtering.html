<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filtering - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Filtering</h1>
            <p class="lead">Learn how to use the filtering feature of the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The filtering feature allows users to filter the table data based on various criteria. All filtering operations are performed without page refresh using AJAX.
            </div>
            
            <div class="section-card">
                <h2>Filter Sidebar</h2>
                <p>The filter sidebar is a slide-in panel that contains filter options. It can be opened by clicking the "Filters" button in the table header.</p>
                <p>Key features of the filter sidebar:</p>
                <ul>
                    <li>Slides in from the left side of the screen</li>
                    <li>Contains various filter options based on your configuration</li>
                    <li>Updates the table data in real-time as filters are changed</li>
                    <li>Stays open until the user explicitly clicks "Apply Filters", "Reset", or "Close"</li>
                </ul>
                <p>The filter sidebar includes the following components:</p>
                <ul>
                    <li>Search input for quick text search</li>
                    <li>Category dropdown for filtering by category</li>
                    <li>Status dropdown for filtering by status</li>
                    <li>Price range inputs for filtering by price</li>
                    <li>Date range inputs for filtering by date</li>
                    <li>"Apply Filters" button to apply all filters</li>
                    <li>"Reset" button to clear all filters</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Configuring Filter Fields</h2>
                <p>You can configure the filter fields in your controller by setting the <code>$filterFields</code> property:</p>
                <pre><code>public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class;
    $this->tableColumns = [
        'id' => 'ID',
        'name' => 'Name',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status',
        'created_at' => 'Date',
    ];
    $this->sortableColumns = ['id', 'name', 'category', 'price', 'status', 'created_at'];
    
    // Configure filter fields
    $this->filterFields = [
        'search' => ['name', 'description'], // Fields to search
        'category' => 'category_id',         // Direct field mapping
        'status' => 'status',
        'price_min' => 'price',              // Price range minimum
        'price_max' => 'price',              // Price range maximum
        'date_from' => 'created_at',         // Date range start
        'date_to' => 'created_at',           // Date range end
    ];
}</code></pre>
                <p>The <code>$filterFields</code> array maps filter parameters to database fields:</p>
                <ul>
                    <li><code>'search' => ['name', 'description']</code>: The search parameter will search in both the name and description fields</li>
                    <li><code>'category' => 'category_id'</code>: The category parameter will filter by the category_id field</li>
                    <li><code>'status' => 'status'</code>: The status parameter will filter by the status field</li>
                    <li><code>'price_min' => 'price'</code>: The price_min parameter will filter by the price field (greater than or equal to)</li>
                    <li><code>'price_max' => 'price'</code>: The price_max parameter will filter by the price field (less than or equal to)</li>
                    <li><code>'date_from' => 'created_at'</code>: The date_from parameter will filter by the created_at field (greater than or equal to)</li>
                    <li><code>'date_to' => 'created_at'</code>: The date_to parameter will filter by the created_at field (less than or equal to)</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Providing Filter Options</h2>
                <p>You need to provide filter options to the table component. This is done in the controller by implementing the <code>getFilterOptions</code> method:</p>
                <pre><code>protected function getFilterOptions()
{
    // Get unique categories for the filter dropdown
    $categories = YourModel::distinct('category')->pluck('category')->toArray();
    
    // Define status options
    $statuses = ['active', 'inactive', 'out_of_stock', 'pending'];
    
    return [
        'categories' => $categories,
        'statuses' => $statuses,
    ];
}</code></pre>
                <p>The <code>getFilterOptions</code> method should return an array with the following keys:</p>
                <ul>
                    <li><code>categories</code>: An array of category options for the category dropdown</li>
                    <li><code>statuses</code>: An array of status options for the status dropdown</li>
                </ul>
                <p>These options will be used to populate the filter dropdowns in the filter sidebar.</p>
            </div>
            
            <div class="section-card">
                <h2>Quick Search</h2>
                <p>In addition to the filter sidebar, the table component also includes a quick search input in the table header. This allows users to quickly search for specific text without opening the filter sidebar.</p>
                <p>The quick search input:</p>
                <ul>
                    <li>Searches in the fields specified in the <code>'search'</code> key of the <code>$filterFields</code> array</li>
                    <li>Updates the table data in real-time as the user types (with a 500ms debounce)</li>
                    <li>Can be used in combination with other filters</li>
                </ul>
                <pre><code>// In your controller
$this->filterFields = [
    'search' => ['name', 'description'], // Quick search will look in these fields
    // Other filter fields...
];</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Real-Time Filtering</h2>
                <p>The Dynamic Table component supports real-time filtering, which means the table data is updated as soon as the user changes a filter option. This is done without page refresh using AJAX.</p>
                <p>Key features of real-time filtering:</p>
                <ul>
                    <li>Select dropdowns update the table immediately when changed</li>
                    <li>Date inputs update the table when changed</li>
                    <li>Number inputs update the table after a short delay (500ms debounce)</li>
                    <li>Quick search updates the table as the user types (with a 500ms debounce)</li>
                </ul>
                <p>The filter sidebar stays open during real-time filtering, allowing users to see the results of their filter changes immediately.</p>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the sorting feature:</p>
                <a href="sorting.html" class="btn btn-primary">Sorting Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
