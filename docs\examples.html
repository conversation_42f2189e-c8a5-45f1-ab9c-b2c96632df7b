<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Examples - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
        .example-tabs .nav-link {
            color: #495057;
            border-radius: 0.5rem 0.5rem 0 0;
            padding: 0.5rem 1rem;
            margin-right: 0.25rem;
        }
        .example-tabs .nav-link.active {
            background-color: #f8f9fa;
            color: #212529;
            border-bottom: none;
        }
        .example-content {
            background-color: #f8f9fa;
            border-radius: 0 0.5rem 0.5rem 0.5rem;
            padding: 1.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Examples</h1>
            <p class="lead">Explore examples of the Dynamic Table component in action.</p>

            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> These examples demonstrate different configurations and use cases for the Dynamic Table component.
            </div>

            <div class="section-card">
                <h2>Basic Table Example</h2>
                <p>A simple table with basic functionality.</p>

                <ul class="nav nav-tabs example-tabs" id="basicTableTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="controller-tab" data-bs-toggle="tab" data-bs-target="#controller" type="button" role="tab" aria-controls="controller" aria-selected="true">Controller</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="view-tab" data-bs-toggle="tab" data-bs-target="#view" type="button" role="tab" aria-controls="view" aria-selected="false">View</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="route-tab" data-bs-toggle="tab" data-bs-target="#route" type="button" role="tab" aria-controls="route" aria-selected="false">Route</button>
                    </li>
                </ul>
                <div class="tab-content example-content" id="basicTableTabsContent">
                    <div class="tab-pane fade show active" id="controller" role="tabpanel" aria-labelledby="controller-tab">
                        <pre><code>namespace App\Http\Controllers;

use App\Exports\ProductExport;
use App\Models\Product;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    use HasDataTable;

    public function __construct()
    {
        $this->tableModel = Product::class;
        $this->exportClass = ProductExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'status' => 'Status',
            'created_at' => 'Date',
        ];
        $this->sortableColumns = ['id', 'name', 'category', 'price', 'status', 'created_at'];

        // Configure filter fields
        $this->filterFields = [
            'search' => ['name', 'description'],
            'category' => 'category',
            'status' => 'status',
            'price_min' => 'price',
            'price_max' => 'price',
            'date_from' => 'created_at',
            'date_to' => 'created_at',
        ];
    }

    public function index(Request $request)
    {
        return $this->handleDataTable($request, 'products.index');
    }

    protected function getFilterOptions()
    {
        // Get unique categories for the filter dropdown
        $categories = Product::distinct('category')->pluck('category')->toArray();

        // Define status options
        $statuses = ['active', 'inactive', 'out_of_stock', 'pending'];

        return [
            'categories' => $categories,
            'statuses' => $statuses,
        ];
    }
}</code></pre>
                    </div>
                    <div class="tab-pane fade" id="view" role="tabpanel" aria-labelledby="view-tab">
                        <pre><code>@extends('layouts.app')

@section('content')
    &lt;div class="container"&gt;
        &lt;x-data-table
            :items="$items"
            :columns="[
                'id' => 'ID',
                'name' => 'Name',
                'category' => 'Category',
                'price' => 'Price',
                'status' => 'Status',
                'created_at' => 'Date',
            ]"
            :sortable="['id', 'name', 'category', 'price', 'status', 'created_at']"
            :filter-options="$filterOptions"
            title="Products"
            route-name="products.index"
        /&gt;
    &lt;/div&gt;
@endsection</code></pre>
                    </div>
                    <div class="tab-pane fade" id="route" role="tabpanel" aria-labelledby="route-tab">
                        <pre><code>// In routes/web.php
Route::get('/products', [ProductController::class, 'index'])->name('products.index');</code></pre>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <h2>Lazy Loading Table Example</h2>
                <p>A table with lazy loading functionality for large datasets.</p>

                <ul class="nav nav-tabs example-tabs" id="lazyTableTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="lazy-controller-tab" data-bs-toggle="tab" data-bs-target="#lazy-controller" type="button" role="tab" aria-controls="lazy-controller" aria-selected="true">Controller</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="lazy-view-tab" data-bs-toggle="tab" data-bs-target="#lazy-view" type="button" role="tab" aria-controls="lazy-view" aria-selected="false">View</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="lazy-route-tab" data-bs-toggle="tab" data-bs-target="#lazy-route" type="button" role="tab" aria-controls="lazy-route" aria-selected="false">Route</button>
                    </li>
                </ul>
                <div class="tab-content example-content" id="lazyTableTabsContent">
                    <div class="tab-pane fade show active" id="lazy-controller" role="tabpanel" aria-labelledby="lazy-controller-tab">
                        <pre><code>namespace App\Http\Controllers;

use App\Exports\ProductExport;
use App\Models\Product;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    use HasDataTable;

    public function __construct()
    {
        $this->tableModel = Product::class;
        $this->exportClass = ProductExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'status' => 'Status',
            'created_at' => 'Date',
        ];
        $this->sortableColumns = ['id', 'name', 'category', 'price', 'status', 'created_at'];

        // Configure filter fields
        $this->filterFields = [
            'search' => ['name', 'description'],
            'category' => 'category',
            'status' => 'status',
            'price_min' => 'price',
            'price_max' => 'price',
            'date_from' => 'created_at',
            'date_to' => 'created_at',
        ];
    }

    public function lazyIndex(Request $request)
    {
        // Add lazy_load parameter to request
        $request->merge(['lazy_load' => true]);

        return $this->handleDataTable($request, 'products.lazy');
    }

    protected function getFilterOptions()
    {
        // Get unique categories for the filter dropdown
        $categories = Product::distinct('category')->pluck('category')->toArray();

        // Define status options
        $statuses = ['active', 'inactive', 'out_of_stock', 'pending'];

        return [
            'categories' => $categories,
            'statuses' => $statuses,
        ];
    }
}</code></pre>
                    </div>
                    <div class="tab-pane fade" id="lazy-view" role="tabpanel" aria-labelledby="lazy-view-tab">
                        <pre><code>@extends('layouts.app')

@section('content')
    &lt;div class="container"&gt;
        &lt;x-lazy-table
            :items="$items"
            :columns="[
                'id' => 'ID',
                'name' => 'Name',
                'category' => 'Category',
                'price' => 'Price',
                'status' => 'Status',
                'created_at' => 'Date',
            ]"
            :sortable="['id', 'name', 'category', 'price', 'status', 'created_at']"
            :filter-options="$filterOptions"
            :total-count="$totalCount"
            :has-more-pages="$hasMorePages"
            :next-page="$nextPage"
            title="Products (Lazy Loading)"
            route-name="products.lazy"
        /&gt;
    &lt;/div&gt;
@endsection</code></pre>
                    </div>
                    <div class="tab-pane fade" id="lazy-route" role="tabpanel" aria-labelledby="lazy-route-tab">
                        <pre><code>// In routes/web.php
Route::get('/products/lazy', [ProductController::class, 'lazyIndex'])->name('products.lazy');</code></pre>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <h2>Custom Rendering Example</h2>
                <p>A table with custom rendering for specific columns.</p>

                <ul class="nav nav-tabs example-tabs" id="customRenderingTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="custom-controller-tab" data-bs-toggle="tab" data-bs-target="#custom-controller" type="button" role="tab" aria-controls="custom-controller" aria-selected="true">Controller</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="custom-view-tab" data-bs-toggle="tab" data-bs-target="#custom-view" type="button" role="tab" aria-controls="custom-view" aria-selected="false">View</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="custom-template-tab" data-bs-toggle="tab" data-bs-target="#custom-template" type="button" role="tab" aria-controls="custom-template" aria-selected="false">Template</button>
                    </li>
                </ul>
                <div class="tab-content example-content" id="customRenderingTabsContent">
                    <div class="tab-pane fade show active" id="custom-controller" role="tabpanel" aria-labelledby="custom-controller-tab">
                        <pre><code>namespace App\Http\Controllers;

use App\Exports\ProductExport;
use App\Models\Product;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    use HasDataTable;

    public function __construct()
    {
        $this->tableModel = Product::class;
        $this->exportClass = ProductExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'status' => 'Status',
            'created_at' => 'Date',
        ];
        $this->sortableColumns = ['id', 'name', 'category', 'price', 'status', 'created_at'];

        // Custom rendering functions
        $this->columnRenderers = [
            'price' => function($item) {
                return '$' . number_format($item->price, 2);
            },
            'status' => function($item) {
                $statusClasses = [
                    'active' => 'success',
                    'inactive' => 'secondary',
                    'out_of_stock' => 'danger',
                    'pending' => 'warning',
                ];
                $class = $statusClasses[$item->status] ?? 'primary';
                return '&lt;span class="badge bg-' . $class . '"&gt;' . ucfirst($item->status) . '&lt;/span&gt;';
            },
            'created_at' => function($item) {
                return $item->created_at->format('Y-m-d');
            },
        ];

        // Configure filter fields
        $this->filterFields = [
            'search' => ['name', 'description'],
            'category' => 'category',
            'status' => 'status',
        ];
    }

    public function customIndex(Request $request)
    {
        return $this->handleDataTable($request, 'products.custom');
    }

    protected function getFilterOptions()
    {
        // Get unique categories for the filter dropdown
        $categories = Product::distinct('category')->pluck('category')->toArray();

        // Define status options
        $statuses = ['active', 'inactive', 'out_of_stock', 'pending'];

        return [
            'categories' => $categories,
            'statuses' => $statuses,
        ];
    }
}</code></pre>
                    </div>
                    <div class="tab-pane fade" id="custom-view" role="tabpanel" aria-labelledby="custom-view-tab">
                        <pre><code>@extends('layouts.app')

@section('content')
    &lt;div class="container"&gt;
        &lt;x-data-table
            :items="$items"
            :columns="[
                'id' => 'ID',
                'name' => 'Name',
                'category' => 'Category',
                'price' => 'Price',
                'status' => 'Status',
                'created_at' => 'Date',
            ]"
            :sortable="['id', 'name', 'category', 'price', 'status', 'created_at']"
            :filter-options="$filterOptions"
            title="Products (Custom Rendering)"
            route-name="products.custom"
        /&gt;
    &lt;/div&gt;
@endsection</code></pre>
                    </div>
                    <div class="tab-pane fade" id="custom-template" role="tabpanel" aria-labelledby="custom-template-tab">
                        <pre><code>@foreach($items as $item)
    &lt;tr&gt;
        @foreach($columns as $key => $column)
            &lt;td&gt;
                @if(isset($columnRenderers[$key]))
                    {!! $columnRenderers[$key]($item) !!}
                @else
                    {{ $item->{$key} }}
                @endif
            &lt;/td&gt;
        @endforeach
        &lt;td&gt;
            &lt;div class="action-buttons"&gt;
                &lt;a href="{{ route('products.show', $item->id) }}" class="btn btn-sm btn-info" title="View"&gt;
                    &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
                &lt;/a&gt;
                &lt;a href="{{ route('products.edit', $item->id) }}" class="btn btn-sm btn-primary" title="Edit"&gt;
                    &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
                &lt;/a&gt;
                &lt;button type="button" class="btn btn-sm btn-danger delete-item" title="Delete" data-id="{{ $item->id }}"&gt;
                    &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
                &lt;/button&gt;
            &lt;/div&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
@endforeach</code></pre>
                    </div>
                </div>
            </div>

            <div class="section-card">
                <h2>Task Manager Example</h2>
                <p>A complex example demonstrating how to use the Dynamic Table component to create a task manager with hierarchical data (projects, modules, tasks) and multiple user assignments.</p>

                <p>This example shows how to:</p>
                <ul>
                    <li>Display hierarchical data in an expandable/collapsible structure</li>
                    <li>Handle many-to-many relationships (tasks to users)</li>
                    <li>Use custom column renderers for complex data</li>
                    <li>Implement visual indicators for different statuses</li>
                    <li>Format dates based on business logic</li>
                </ul>

                <a href="task-manager-example.html" class="btn btn-primary">View Task Manager Example</a>
            </div>

            <div class="mt-4">
                <p>If you encounter any issues, check the troubleshooting guide:</p>
                <a href="troubleshooting.html" class="btn btn-primary">Troubleshooting Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
