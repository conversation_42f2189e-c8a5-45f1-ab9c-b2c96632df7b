<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customization - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Customization</h1>
            <p class="lead">Learn how to customize the Dynamic Table component to fit your needs.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The Dynamic Table component is highly customizable. You can customize its appearance, behavior, and functionality to match your application's requirements.
            </div>
            
            <div class="section-card">
                <h2>Customizing Table Appearance</h2>
                <p>You can customize the appearance of the table by modifying the CSS or adding custom classes.</p>
                
                <h3>CSS Customization</h3>
                <p>The table component uses a dedicated CSS file (<code>dynamic-table.css</code>) that you can modify to change the appearance of the table. The CSS file is located in the <code>public/css</code> directory.</p>
                <p>Key CSS classes that you can customize:</p>
                <ul>
                    <li><code>.dynamic-table-container</code>: The main container for the table</li>
                    <li><code>.table-header</code>: The header section of the table</li>
                    <li><code>.data-table</code>: The table element itself</li>
                    <li><code>.table-footer</code>: The footer section of the table</li>
                    <li><code>.filter-sidebar</code>: The filter sidebar</li>
                    <li><code>.pagination-container</code>: The pagination controls</li>
                </ul>
                <p>Example of customizing the table header:</p>
                <pre><code>/* In your custom CSS file */
.table-header {
    background-color: #f0f4f8;
    border-bottom: 2px solid #d1d5db;
    padding: 1.5rem;
}

.table-header h2 {
    color: #1e40af;
    font-weight: 600;
}</code></pre>
                
                <h3>Custom Classes</h3>
                <p>You can add custom classes to the table using the <code>:tableClass</code> prop:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    tableClass="my-custom-table"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
                <p>The custom class will be added to the <code>&lt;table&gt;</code> element, allowing you to target it with CSS:</p>
                <pre><code>/* In your custom CSS file */
.my-custom-table {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.my-custom-table th {
    background-color: #f8fafc;
    color: #334155;
}</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Customizing Table Behavior</h2>
                <p>You can customize the behavior of the table by modifying the JavaScript or using component props.</p>
                
                <h3>JavaScript Customization</h3>
                <p>The table component uses a dedicated JavaScript file (<code>dynamic-table.js</code>) that you can modify to change the behavior of the table. The JavaScript file is located in the <code>public/js</code> directory.</p>
                <p>Key JavaScript functions that you can customize:</p>
                <ul>
                    <li><code>fetchTableData</code>: Handles AJAX requests for table data</li>
                    <li><code>loadMoreItems</code>: Handles lazy loading of additional items</li>
                    <li><code>toggleFilterSidebar</code>: Handles opening and closing the filter sidebar</li>
                    <li><code>setupEventListeners</code>: Sets up event listeners for table interactions</li>
                </ul>
                <p>Example of customizing the loading indicator:</p>
                <pre><code>// In your custom JavaScript file
function showLoading() {
    if (elements.tableBody.length) {
        // Create a custom loading overlay
        const loadingOverlay = $('&lt;div class="custom-loading-overlay"&gt;&lt;div class="spinner"&gt;&lt;/div&gt;&lt;/div&gt;');
        elements.tableBody.parent().append(loadingOverlay);
        elements.tableBody.css('opacity', '0.5');
    }
}

function hideLoading() {
    if (elements.tableBody.length) {
        // Remove the custom loading overlay
        elements.tableBody.parent().find('.custom-loading-overlay').remove();
        elements.tableBody.css('opacity', '1');
    }
}</code></pre>
                
                <h3>Component Props</h3>
                <p>You can customize the behavior of the table using component props:</p>
                <ul>
                    <li><code>:searchable</code>: Whether to enable search (default: <code>true</code>)</li>
                    <li><code>:filterable</code>: Whether to enable filters (default: <code>true</code>)</li>
                    <li><code>:pagination</code>: Whether to enable pagination (default: <code>true</code>)</li>
                    <li><code>:exportable</code>: Whether to enable export functionality (default: <code>true</code>)</li>
                    <li><code>:export-formats</code>: Available export formats (default: <code>['excel', 'csv', 'pdf']</code>)</li>
                </ul>
                <p>Example of disabling search and export:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    :searchable="false"
    :exportable="false"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Customizing Table Content</h2>
                <p>You can customize the content of the table by modifying the Blade templates or using custom rendering functions.</p>
                
                <h3>Blade Templates</h3>
                <p>The table component uses several Blade templates that you can modify to change the content of the table:</p>
                <ul>
                    <li><code>components/data-table/table.blade.php</code>: The main table template</li>
                    <li><code>components/data-table/table-rows.blade.php</code>: The template for table rows</li>
                    <li><code>components/data-table/pagination.blade.php</code>: The template for pagination controls</li>
                </ul>
                <p>Example of customizing the table rows template:</p>
                <pre><code>@foreach($items as $item)
    &lt;tr&gt;
        &lt;td&gt;{{ $item->id }}&lt;/td&gt;
        &lt;td&gt;
            &lt;div class="d-flex align-items-center"&gt;
                @if($item->image_url)
                    &lt;div class="item-image me-3"&gt;
                        &lt;img src="{{ $item->image_url }}" alt="{{ $item->name }}"&gt;
                    &lt;/div&gt;
                @endif
                &lt;div&gt;
                    &lt;div class="fw-bold"&gt;{{ $item->name }}&lt;/div&gt;
                    &lt;div class="text-muted small"&gt;{{ Str::limit($item->description, 50) }}&lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/td&gt;
        &lt;td&gt;{{ $item->category }}&lt;/td&gt;
        &lt;td&gt;${{ number_format($item->price, 2) }}&lt;/td&gt;
        &lt;td&gt;
            &lt;span class="status-badge status-{{ $item->status }}"&gt;
                {{ ucfirst($item->status) }}
            &lt;/span&gt;
        &lt;/td&gt;
        &lt;td&gt;{{ $item->created_at->format('Y-m-d') }}&lt;/td&gt;
        &lt;td&gt;
            &lt;div class="action-buttons"&gt;
                &lt;a href="{{ route('products.show', $item->id) }}" class="btn-view" title="View"&gt;
                    &lt;i class="fas fa-eye"&gt;&lt;/i&gt;
                &lt;/a&gt;
                &lt;a href="{{ route('products.edit', $item->id) }}" class="btn-edit" title="Edit"&gt;
                    &lt;i class="fas fa-edit"&gt;&lt;/i&gt;
                &lt;/a&gt;
                &lt;button type="button" class="btn-delete" title="Delete" data-id="{{ $item->id }}"&gt;
                    &lt;i class="fas fa-trash"&gt;&lt;/i&gt;
                &lt;/button&gt;
            &lt;/div&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
@endforeach</code></pre>
                
                <h3>Custom Rendering Functions</h3>
                <p>You can create custom rendering functions in your controller to customize how specific columns are displayed:</p>
                <pre><code>public function __construct()
{
    $this->tableModel = Product::class;
    $this->exportClass = ProductExport::class;
    $this->tableColumns = [
        'id' => 'ID',
        'name' => 'Name',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status',
        'created_at' => 'Date',
    ];
    
    // Custom rendering functions
    $this->columnRenderers = [
        'price' => function($item) {
            return '$' . number_format($item->price, 2);
        },
        'status' => function($item) {
            return '&lt;span class="status-badge status-' . $item->status . '"&gt;' . ucfirst($item->status) . '&lt;/span&gt;';
        },
        'created_at' => function($item) {
            return $item->created_at->format('Y-m-d');
        },
    ];
}</code></pre>
                <p>Then, in your table rows template, you can use these rendering functions:</p>
                <pre><code>@foreach($items as $item)
    &lt;tr&gt;
        @foreach($columns as $key => $column)
            &lt;td&gt;
                @if(isset($columnRenderers[$key]))
                    {!! $columnRenderers[$key]($item) !!}
                @else
                    {{ $item->{$key} }}
                @endif
            &lt;/td&gt;
        @endforeach
        &lt;td&gt;
            &lt;!-- Actions --&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
@endforeach</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Customizing Filter Options</h2>
                <p>You can customize the filter options by modifying the <code>getFilterOptions</code> method in your controller:</p>
                <pre><code>protected function getFilterOptions()
{
    // Get unique categories for the filter dropdown
    $categories = Category::pluck('name', 'id')->toArray();
    
    // Define status options with custom labels
    $statuses = [
        'active' => 'Active',
        'inactive' => 'Inactive',
        'out_of_stock' => 'Out of Stock',
        'pending' => 'Pending Approval',
    ];
    
    // Define price ranges
    $priceRanges = [
        ['min' => 0, 'max' => 50, 'label' => 'Under $50'],
        ['min' => 50, 'max' => 100, 'label' => '$50 - $100'],
        ['min' => 100, 'max' => 200, 'label' => '$100 - $200'],
        ['min' => 200, 'max' => null, 'label' => 'Over $200'],
    ];
    
    return [
        'categories' => $categories,
        'statuses' => $statuses,
        'priceRanges' => $priceRanges,
    ];
}</code></pre>
                <p>Then, in your filter sidebar template, you can use these custom filter options:</p>
                <pre><code>&lt;div class="filter-section"&gt;
    &lt;h4&gt;Price Range&lt;/h4&gt;
    &lt;div class="filter-input"&gt;
        &lt;select name="price_range" id="priceRangeFilter"&gt;
            &lt;option value=""&gt;All Prices&lt;/option&gt;
            @foreach($filterOptions['priceRanges'] as $range)
                &lt;option value="{{ json_encode(['min' => $range['min'], 'max' => $range['max']]) }}"&gt;
                    {{ $range['label'] }}
                &lt;/option&gt;
            @endforeach
        &lt;/select&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
            </div>
            
            <div class="mt-4">
                <p>Next, see examples of the Dynamic Table component in action:</p>
                <a href="examples.html" class="btn btn-primary">Examples</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
