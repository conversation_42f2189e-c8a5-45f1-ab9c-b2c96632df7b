<html class="scroll-smooth" lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <title>
   <PERSON><PERSON>
  </title>
  <script src="https://cdn.tailwindcss.com">
  </script>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" rel="stylesheet"/>
  <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700&amp;family=Open+Sans&amp;display=swap" rel="stylesheet"/>
  <style>
   body {
      font-family: 'Open Sans', sans-serif;
      background-color: #0f172a;
      overflow: hidden;
      margin: 0;
      height: 100vh;
      width: 100vw;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    .animated-light {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 400px;
      height: 400px;
      margin-left: -200px;
      margin-top: -200px;
      border-radius: 50%;
      background: radial-gradient(circle, #fef3c7 0%, #fbbf24 40%, transparent 70%);
      filter: drop-shadow(0 0 15px #fbbf24);
      animation: pulseLight 4s ease-in-out infinite;
      z-index: 0;
      mix-blend-mode: screen;
    }
    @keyframes pulseLight {
      0%, 100% {
        transform: scale(1);
        opacity: 0.7;
        filter: drop-shadow(0 0 15px #fbbf24);
      }
      50% {
        transform: scale(1.15);
        opacity: 1;
        filter: drop-shadow(0 0 30px #fbbf24);
      }
    }
    .soul-body {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 280px;
      height: 400px;
      margin-left: -140px;
      margin-top: -200px;
      z-index: 1;
      animation: floatSoul 6s ease-in-out infinite;
      filter: drop-shadow(0 0 10px #fbbf24);
    }
    @keyframes floatSoul {
      0%, 100% {
        transform: translateY(0);
        opacity: 0.85;
      }
      50% {
        transform: translateY(-15px);
        opacity: 1;
      }
    }
    .star {
      position: absolute;
      background: radial-gradient(circle, #fff 60%, transparent 80%);
      border-radius: 50%;
      opacity: 0.8;
      animation: twinkle 3s infinite ease-in-out;
      filter: drop-shadow(0 0 4px #fff);
    }
    @keyframes twinkle {
      0%, 100% {
        opacity: 0.8;
        transform: scale(1);
      }
      50% {
        opacity: 0.3;
        transform: scale(0.7);
      }
    }
  </style>
 </head>
 <body>
  <div aria-hidden="true" class="animated-light">
  </div>
  <img alt="A glowing, ethereal soul body silhouette with soft golden light and divine aura, floating gently" aria-hidden="true" class="soul-body" height="400" src="https://storage.googleapis.com/a1aa/image/9405f49f-5fc3-4e5a-1e55-be527ecef5d2.jpg" width="280"/>
  <h1 class="relative z-10 text-center text-5xl sm:text-7xl font-montserrat font-extrabold text-yellow-400 drop-shadow-lg select-none">
   Karan Sarir
  </h1>
  <!-- Twinkling stars -->
  <div class="star" style="top:10%; left:15%; width:8px; height:8px; animation-delay: 0s;">
  </div>
  <div class="star" style="top:20%; left:80%; width:6px; height:6px; animation-delay: 1.2s;">
  </div>
  <div class="star" style="top:35%; left:40%; width:5px; height:5px; animation-delay: 0.7s;">
  </div>
  <div class="star" style="top:50%; left:70%; width:7px; height:7px; animation-delay: 1.5s;">
  </div>
  <div class="star" style="top:65%; left:25%; width:6px; height:6px; animation-delay: 0.3s;">
  </div>
  <div class="star" style="top:75%; left:85%; width:8px; height:8px; animation-delay: 2s;">
  </div>
  <div class="star" style="top:85%; left:50%; width:5px; height:5px; animation-delay: 1s;">
  </div>
  <div class="star" style="top:15%; left:60%; width:7px; height:7px; animation-delay: 0.5s;">
  </div>
  <div class="star" style="top:40%; left:10%; width:6px; height:6px; animation-delay: 1.8s;">
  </div>
  <div class="star" style="top:55%; left:35%; width:5px; height:5px; animation-delay: 0.9s;">
  </div>
 </body>
</html>
