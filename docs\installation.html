<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .step-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        .step-number {
            display: inline-block;
            width: 30px;
            height: 30px;
            background-color: #4f46e5;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 30px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Installation Guide</h1>
            <p class="lead">Follow these steps to install the Dynamic Table component in your Laravel project.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> This component requires Laravel 8.0+ and PHP 7.4+.
            </div>
            
            <div class="step-card">
                <h3><span class="step-number">1</span> Install Required Packages</h3>
                <p>First, install the required packages for export functionality:</p>
                <pre><code>composer require maatwebsite/excel barryvdh/laravel-dompdf</code></pre>
                <p>These packages are used for exporting data to Excel, CSV, and PDF formats.</p>
            </div>
            
            <div class="step-card">
                <h3><span class="step-number">2</span> Copy Required Files</h3>
                <p>Copy the following files to your Laravel project:</p>
                <pre><code>app/
├── Exports/
│   └── ProductsExport.php
├── Traits/
│   └── HasDataTable.php
├── View/
│   └── Components/
│       ├── DataTable.php
│       └── LazyTable.php
public/
├── css/
│   └── dynamic-table.css
├── js/
│   └── dynamic-table.js
resources/
└── views/
    ├── components/
    │   ├── data-table/
    │   │   ├── pagination.blade.php
    │   │   ├── table-rows.blade.php
    │   │   └── table.blade.php
    │   └── lazy-table/
    │       ├── table-rows.blade.php
    │       └── table.blade.php
    └── exports/
        └── products-pdf.blade.php</code></pre>
                <p>You can download these files from the <a href="#">GitHub repository</a> or copy them from the source code.</p>
            </div>
            
            <div class="step-card">
                <h3><span class="step-number">3</span> Register Components</h3>
                <p>Add the following to your <code>app/Providers/AppServiceProvider.php</code>:</p>
                <pre><code>use App\View\Components\DataTable;
use App\View\Components\LazyTable;
use Illuminate\Support\Facades\Blade;

public function boot()
{
    // Register components
    Blade::component('data-table', DataTable::class);
    Blade::component('lazy-table', LazyTable::class);
}</code></pre>
            </div>
            
            <div class="step-card">
                <h3><span class="step-number">4</span> Include CSS and JavaScript</h3>
                <p>Add the following to your layout file:</p>
                <pre><code>&lt;!-- In your &lt;head&gt; section --&gt;
&lt;meta name="csrf-token" content="{{ csrf_token() }}"&gt;
&lt;link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"&gt;
&lt;link rel="stylesheet" href="{{ asset('css/dynamic-table.css') }}"&gt;

&lt;!-- Before closing &lt;/body&gt; tag --&gt;
&lt;script src="https://code.jquery.com/jquery-3.6.0.min.js"&gt;&lt;/script&gt;
&lt;script src="{{ asset('js/dynamic-table.js') }}"&gt;&lt;/script&gt;</code></pre>
                <p>The component requires jQuery, Font Awesome, and the custom CSS and JavaScript files.</p>
            </div>
            
            <div class="step-card">
                <h3><span class="step-number">5</span> Create an Export Class</h3>
                <p>Create a custom export class for your model:</p>
                <pre><code>namespace App\Exports;

use App\Models\YourModel;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class YourExportClass implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request ?? request();
    }

    public function collection()
    {
        // Build your query here, similar to the controller
        $query = YourModel::query();
        
        // Apply filters based on request
        // ...
        
        return $query->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            // Add your headings here
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->name,
            // Map your data here
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}</code></pre>
            </div>
            
            <div class="alert alert-success mt-4" role="alert">
                <i class="fas fa-check-circle me-2"></i> You have successfully installed the Dynamic Table component! Now you can start using it in your Laravel project.
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the component in your project:</p>
                <a href="basic-usage.html" class="btn btn-primary">Basic Usage Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
