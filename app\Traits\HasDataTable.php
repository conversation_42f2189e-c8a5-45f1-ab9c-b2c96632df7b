<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

trait HasDataTable
{
    /**
     * The model class to use for the table.
     *
     * @var string
     */
    protected $tableModel;

    /**
     * The export class to use for the table.
     *
     * @var string
     */
    protected $exportClass;

    /**
     * The view to use for PDF exports.
     *
     * @var string
     */
    protected $pdfView = 'exports.products-pdf';

    /**
     * The columns to display in the table.
     *
     * @var array
     */
    protected $tableColumns = [];

    /**
     * The columns that can be sorted.
     *
     * @var array
     */
    protected $sortableColumns = [];

    /**
     * The default sort column.
     *
     * @var string
     */
    protected $defaultSortColumn = 'id';

    /**
     * The default sort direction.
     *
     * @var string
     */
    protected $defaultSortDirection = 'asc';

    /**
     * The default number of items per page.
     *
     * @var int
     */
    protected $defaultPerPage = 10;

    /**
     * The filter fields for the table.
     *
     * @var array
     */
    protected $filterFields = [
        'search' => ['name', 'description', 'category'],
        'category' => 'category',
        'status' => 'status',
        'price_min' => 'price',
        'price_max' => 'price',
        'date_from' => 'created_at',
        'date_to' => 'created_at',
    ];

    /**
     * Handle the data table request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $viewPath
     * @param  array  $extraData
     * @return \Illuminate\Http\Response
     */
    protected function handleDataTable(Request $request, string $viewPath, array $extraData = [])
    {
        // Check if export request
        if ($request->has('export')) {
            return $this->exportData($request, $request->input('export'));
        }

        $query = $this->buildTableQuery($request);

        // For standard pagination
        if (!$request->filled('lazy_load')) {
            $perPage = $request->input('per_page', $this->defaultPerPage);
            $items = $query->paginate($perPage)->withQueryString();

            // Get filter options
            $filterOptions = $this->getFilterOptions();

            // Check if this is an AJAX request
            if ($request->ajax() || $request->wantsJson()) {
                $response = [
                    'table' => view('components.data-table.table-rows', ['items' => $items])->render(),
                    'pagination' => view('components.data-table.pagination', ['items' => $items])->render(),
                    'success' => true,
                    'message' => 'Data loaded successfully',
                    'total' => $items->total(),
                    'per_page' => $items->perPage(),
                    'current_page' => $items->currentPage(),
                    'last_page' => $items->lastPage(),
                    'append' => false,
                    'currentCount' => $items->count(),
                    'totalCount' => $items->total(),
                    'sort' => $request->input('sort'),
                    'direction' => $request->input('direction', 'asc'),
                ];

                // If this is a lazy load table, add additional data
                if ($request->has('lazy_load')) {
                    $hasMorePages = $items->hasMorePages();
                    $response['hasMorePages'] = $hasMorePages;
                    $response['nextPage'] = $hasMorePages ? $items->currentPage() + 1 : null;
                }

                return response()->json($response);
            }

            return view($viewPath, array_merge([
                'items' => $items,
                'filterOptions' => $filterOptions,
            ], $extraData));
        }

        // For lazy loading
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', $this->defaultPerPage);
        $offset = ($page - 1) * $perPage;

        // Get total count for the query
        $totalCount = $query->count();

        // Get the paginated results
        $items = $query->skip($offset)->take($perPage)->get();

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        // Check if this is an AJAX request for lazy loading
        if ($request->ajax() || $request->wantsJson()) {
            $hasMorePages = ($offset + $perPage) < $totalCount;
            $currentCount = $offset + $items->count();

            // Determine if this is a filter/sort operation or a lazy load append operation
            $isAppend = $request->has('page') && $page > 1 && !$request->has('filter_change');

            $response = [
                'html' => view('components.lazy-table.table-rows', ['items' => $items])->render(),
                'hasMorePages' => $hasMorePages,
                'nextPage' => $hasMorePages ? $page + 1 : null,
                'totalCount' => $totalCount,
                'currentCount' => $currentCount,
                'success' => true,
                'message' => "Loaded items " . ($offset + 1) . " to " . $currentCount . " of " . $totalCount,
                'append' => $isAppend,
                'sort' => $request->input('sort'),
                'direction' => $request->input('direction', 'asc'),
                'debug' => [
                    'page' => $page,
                    'perPage' => $perPage,
                    'offset' => $offset,
                    'itemCount' => $items->count(),
                    'request' => $request->all()
                ]
            ];

            // If this is not an append operation (i.e., it's a filter/sort), also include the full table
            if (!$isAppend) {
                $response['table'] = view('components.lazy-table.table-rows', ['items' => $items])->render();
            }

            return response()->json($response);
        }

        return view($viewPath, array_merge([
            'items' => $items,
            'filterOptions' => $filterOptions,
            'totalCount' => $totalCount,
            'hasMorePages' => ($offset + $perPage) < $totalCount,
            'nextPage' => 2, // Start with page 2 for lazy loading
        ], $extraData));
    }

    /**
     * Build the query for the table.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function buildTableQuery(Request $request): Builder
    {
        $modelClass = $this->tableModel;
        $query = $modelClass::query();

        // Apply filters
        foreach ($this->filterFields as $requestField => $modelFields) {
            if ($request->filled($requestField)) {
                $this->applyFilter($query, $requestField, $modelFields, $request->input($requestField));
            }
        }

        // Apply sorting
        $sortField = $request->input('sort', $this->defaultSortColumn);
        $sortDirection = $request->input('direction', $this->defaultSortDirection);
        $query->orderBy($sortField, $sortDirection);

        return $query;
    }

    /**
     * Apply a filter to the query.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $requestField
     * @param  string|array  $modelFields
     * @param  mixed  $value
     * @return void
     */
    protected function applyFilter(Builder $query, string $requestField, $modelFields, $value): void
    {
        if ($requestField === 'search' && is_array($modelFields)) {
            $query->where(function ($q) use ($modelFields, $value) {
                foreach ($modelFields as $field) {
                    $q->orWhere($field, 'like', "%{$value}%");
                }
            });
        } elseif ($requestField === 'price_min') {
            $query->where($modelFields, '>=', $value);
        } elseif ($requestField === 'price_max') {
            $query->where($modelFields, '<=', $value);
        } elseif ($requestField === 'date_from') {
            $query->whereDate($modelFields, '>=', $value);
        } elseif ($requestField === 'date_to') {
            $query->whereDate($modelFields, '<=', $value);
        } else {
            $query->where($modelFields, $value);
        }
    }

    /**
     * Get filter options for the table.
     *
     * @return array
     */
    protected function getFilterOptions(): array
    {
        $modelClass = $this->tableModel;

        return [
            'categories' => $modelClass::distinct()->pluck('category'),
            'statuses' => ['active', 'inactive', 'out_of_stock', 'pending'],
        ];
    }

    /**
     * Export data in various formats.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $format
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse|\Illuminate\Http\Response
     */
    protected function exportData(Request $request, string $format)
    {
        $exportClass = $this->exportClass;

        switch ($format) {
            case 'excel':
                return Excel::download(new $exportClass($request), 'export.xlsx');

            case 'csv':
                return Excel::download(new $exportClass($request), 'export.csv', \Maatwebsite\Excel\Excel::CSV);

            case 'pdf':
                $query = $this->buildTableQuery($request);
                $items = $query->get();

                $pdf = PDF::loadView($this->pdfView, [
                    'products' => $items,
                    'title' => 'Data Export',
                    'date' => now()->format('Y-m-d H:i:s')
                ]);

                return $pdf->download('export.pdf');

            default:
                return back()->with('error', 'Invalid export format');
        }
    }
}
