<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagination - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Pagination</h1>
            <p class="lead">Learn how to use the pagination feature of the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The pagination feature allows users to navigate through large datasets by dividing the data into pages. All pagination operations are performed without page refresh using AJAX.
            </div>
            
            <div class="section-card">
                <h2>Standard Pagination</h2>
                <p>The Dynamic Table component includes standard pagination with the following features:</p>
                <ul>
                    <li>Page navigation links (First, Previous, Page Numbers, Next, Last)</li>
                    <li>Items per page selector (10, 25, 50, 100)</li>
                    <li>Page information display (Showing X to Y of Z entries)</li>
                    <li>AJAX-based navigation (no page refresh)</li>
                </ul>
                <p>The pagination is automatically enabled for the table component. You can disable it by setting the <code>:pagination</code> prop to <code>false</code>:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    :pagination="false"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Configuring Pagination</h2>
                <p>The pagination is configured using Laravel's built-in pagination system. You can customize the number of items per page in your controller:</p>
                <pre><code>public function handleDataTable(Request $request, $view)
{
    // Build the query
    $query = $this->buildQuery($request);
    
    // Get the items with pagination
    $perPage = $request->input('per_page', 10); // Default to 10 items per page
    $items = $query->paginate($perPage);
    
    // Rest of the method...
}</code></pre>
                <p>The <code>per_page</code> parameter can be changed by the user using the items per page selector in the table footer.</p>
            </div>
            
            <div class="section-card">
                <h2>Pagination Controls</h2>
                <p>The pagination controls are displayed in the table footer and include:</p>
                <h3>Page Navigation Links</h3>
                <ul>
                    <li><strong>First Page</strong>: Takes the user to the first page</li>
                    <li><strong>Previous Page</strong>: Takes the user to the previous page</li>
                    <li><strong>Page Numbers</strong>: Direct links to specific pages</li>
                    <li><strong>Next Page</strong>: Takes the user to the next page</li>
                    <li><strong>Last Page</strong>: Takes the user to the last page</li>
                </ul>
                <p>The current page is highlighted, and disabled links are shown for navigation that is not applicable (e.g., Previous Page on the first page).</p>
                
                <h3>Items Per Page Selector</h3>
                <p>The items per page selector allows users to change the number of items displayed per page. The available options are:</p>
                <ul>
                    <li>10 items per page (default)</li>
                    <li>25 items per page</li>
                    <li>50 items per page</li>
                    <li>100 items per page</li>
                </ul>
                <p>When the user changes the items per page, the table is automatically updated to show the selected number of items, and the pagination is reset to the first page.</p>
                
                <h3>Page Information</h3>
                <p>The page information display shows the current range of items being displayed and the total number of items:</p>
                <pre><code>Showing 1 to 10 of 100 entries</code></pre>
            </div>
            
            <div class="section-card">
                <h2>AJAX-Based Pagination</h2>
                <p>All pagination operations are performed without page refresh using AJAX. When a user clicks on a pagination link or changes the items per page, the following happens:</p>
                <ol>
                    <li>The click event is intercepted by JavaScript</li>
                    <li>An AJAX request is sent to the server with the appropriate pagination parameters</li>
                    <li>The server returns the new table data and pagination controls</li>
                    <li>The table content and pagination controls are updated dynamically</li>
                    <li>The browser URL is updated to reflect the new pagination state (without refreshing the page)</li>
                </ol>
                <p>This provides a smooth user experience without the need to reload the entire page.</p>
            </div>
            
            <div class="section-card">
                <h2>Combining Pagination with Filtering and Sorting</h2>
                <p>The pagination feature can be combined with filtering and sorting. When a user applies filters or sorts the table, the pagination is reset to the first page, but the filters and sort parameters are preserved in the pagination links.</p>
                <p>For example, if a user filters the table to show only active products, sorts by name, and then navigates to the second page, the pagination link will include all these parameters:</p>
                <pre><code>&lt;a href="/your-table?status=active&sort=name&direction=asc&page=2" class="page-link"&gt;2&lt;/a&gt;</code></pre>
                <p>This ensures that the filters and sort order are preserved when navigating between pages.</p>
            </div>
            
            <div class="section-card">
                <h2>Scroll Behavior</h2>
                <p>When using pagination, the table content is scrollable within its container, but the pagination controls remain fixed at the bottom of the table. This allows users to scroll through the table data without losing access to the pagination controls.</p>
                <p>When a user navigates to a different page, the table scrolls back to the top automatically to show the new page of data.</p>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the lazy loading feature:</p>
                <a href="lazy-loading.html" class="btn btn-primary">Lazy Loading Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
