<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class Cors
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $corsConfig = config('cors');

        // Get allowed origins
        $allowedOrigins = $corsConfig['allowed_origins'] ?? ['*'];
        $origin = $request->header('Origin');

        // Determine if origin is allowed
        $allowOrigin = '*';
        if (!in_array('*', $allowedOrigins) && $origin) {
            $allowOrigin = in_array($origin, $allowedOrigins) ? $origin : null;
        }

        // Handle preflight OPTIONS request
        if ($request->getMethod() === "OPTIONS") {
            $response = response('', 200);

            if ($allowOrigin) {
                $response->header('Access-Control-Allow-Origin', $allowOrigin);
            }

            $response->header('Access-Control-Allow-Methods', implode(', ', $corsConfig['allowed_methods'] ?? ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH']))
                ->header('Access-Control-Allow-Headers', implode(', ', $corsConfig['allowed_headers'] ?? ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-TOKEN', 'Accept', 'Origin']))
                ->header('Access-Control-Allow-Credentials', $corsConfig['supports_credentials'] ? 'true' : 'false')
                ->header('Access-Control-Max-Age', $corsConfig['max_age'] ?? 86400);

            return $response;
        }

        $response = $next($request);

        // Add CORS headers to all responses
        if ($allowOrigin) {
            $response->headers->set('Access-Control-Allow-Origin', $allowOrigin);
        }

        $response->headers->set('Access-Control-Allow-Methods', implode(', ', $corsConfig['allowed_methods'] ?? ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH']));
        $response->headers->set('Access-Control-Allow-Headers', implode(', ', $corsConfig['allowed_headers'] ?? ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-TOKEN', 'Accept', 'Origin']));
        $response->headers->set('Access-Control-Allow-Credentials', $corsConfig['supports_credentials'] ? 'true' : 'false');

        // Add exposed headers if configured
        if (!empty($corsConfig['exposed_headers'])) {
            $response->headers->set('Access-Control-Expose-Headers', implode(', ', $corsConfig['exposed_headers']));
        }

        return $response;
    }
}
