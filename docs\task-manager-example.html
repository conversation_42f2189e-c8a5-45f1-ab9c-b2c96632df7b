<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Manager Example - Dynamic Table Component</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 20px;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-completed {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .status-in-progress {
            background-color: #cff4fc;
            color: #055160;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #664d03;
        }
        .status-blocked {
            background-color: #f8d7da;
            color: #842029;
        }
        .user-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            margin-right: 0.25rem;
            margin-bottom: 0.25rem;
            background-color: #e9ecef;
        }
        .task-row {
            background-color: #f8f9fa;
        }
        .module-row {
            background-color: #e9ecef;
        }
        .project-row {
            background-color: #dee2e6;
            font-weight: bold;
        }
        .indent-1 {
            padding-left: 1.5rem !important;
        }
        .indent-2 {
            padding-left: 3rem !important;
        }
        .table-preview {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            overflow: hidden;
            margin: 2rem 0;
        }
        .table-preview table {
            margin-bottom: 0;
        }
        .expand-btn {
            cursor: pointer;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            border-radius: 50%;
            background-color: #e9ecef;
            display: inline-block;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Task Manager Example with Dynamic Table</h1>
        <p class="lead">This example demonstrates how to use the Dynamic Table component to create a task manager with hierarchical data (projects, modules, tasks) and multiple user assignments.</p>
        
        <div class="section-card">
            <h2>Data Structure</h2>
            <p>In this example, we have a hierarchical data structure:</p>
            <ul>
                <li><strong>Projects</strong> - Top level items</li>
                <li><strong>Modules</strong> - Belong to projects</li>
                <li><strong>Tasks</strong> - Belong to modules</li>
            </ul>
            <p>Each task can be assigned to multiple users and has a status (Completed, In Progress, Pending, Blocked).</p>
            
            <h3>Database Structure</h3>
            <pre><code>// projects table
Schema::create('projects', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->text('description')->nullable();
    $table->date('start_date');
    $table->date('end_date');
    $table->timestamps();
});

// modules table
Schema::create('modules', function (Blueprint $table) {
    $table->id();
    $table->foreignId('project_id')->constrained()->onDelete('cascade');
    $table->string('name');
    $table->text('description')->nullable();
    $table->timestamps();
});

// tasks table
Schema::create('tasks', function (Blueprint $table) {
    $table->id();
    $table->foreignId('module_id')->constrained()->onDelete('cascade');
    $table->string('name');
    $table->text('description')->nullable();
    $table->enum('status', ['completed', 'in_progress', 'pending', 'blocked']);
    $table->date('due_date')->nullable();
    $table->timestamps();
});

// users table
Schema::create('users', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('email')->unique();
    $table->timestamp('email_verified_at')->nullable();
    $table->string('password');
    $table->rememberToken();
    $table->timestamps();
});

// task_user pivot table
Schema::create('task_user', function (Blueprint $table) {
    $table->id();
    $table->foreignId('task_id')->constrained()->onDelete('cascade');
    $table->foreignId('user_id')->constrained()->onDelete('cascade');
    $table->timestamps();
});</code></pre>
        </div>
        
        <div class="section-card">
            <h2>Models</h2>
            <p>Here are the Eloquent models for our task manager:</p>
            
            <h3>Project Model</h3>
            <pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    protected $fillable = ['name', 'description', 'start_date', 'end_date'];
    
    public function modules()
    {
        return $this->hasMany(Module::class);
    }
    
    public function tasks()
    {
        return $this->hasManyThrough(Task::class, Module::class);
    }
}</code></pre>
            
            <h3>Module Model</h3>
            <pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Module extends Model
{
    protected $fillable = ['project_id', 'name', 'description'];
    
    public function project()
    {
        return $this->belongsTo(Project::class);
    }
    
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }
}</code></pre>
            
            <h3>Task Model</h3>
            <pre><code>namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Task extends Model
{
    protected $fillable = ['module_id', 'name', 'description', 'status', 'due_date'];
    
    public function module()
    {
        return $this->belongsTo(Module::class);
    }
    
    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}</code></pre>
            
            <h3>User Model</h3>
            <pre><code>namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use Notifiable;
    
    protected $fillable = ['name', 'email', 'password'];
    
    protected $hidden = ['password', 'remember_token'];
    
    public function tasks()
    {
        return $this->belongsToMany(Task::class);
    }
}</code></pre>
        </div>
        
        <div class="section-card">
            <h2>Controller</h2>
            <p>Here's the controller for our task manager that uses the HasDataTable trait:</p>
            
            <pre><code>namespace App\Http\Controllers;

use App\Exports\TasksExport;
use App\Models\Project;
use App\Models\Task;
use App\Models\User;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TaskManagerController extends Controller
{
    use HasDataTable;
    
    public function __construct()
    {
        $this->tableModel = Task::class;
        $this->exportClass = TasksExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'project' => 'Project',
            'module' => 'Module',
            'name' => 'Task',
            'users' => 'Assigned To',
            'status' => 'Status',
            'due_date' => 'Due Date',
        ];
        $this->sortableColumns = ['id', 'name', 'status', 'due_date'];
        
        // Configure filter fields
        $this->filterFields = [
            'search' => ['tasks.name', 'modules.name', 'projects.name'],
            'project_id' => 'projects.id',
            'status' => 'tasks.status',
            'user_id' => 'users.id',
            'due_date_from' => 'tasks.due_date',
            'due_date_to' => 'tasks.due_date',
        ];
        
        // Custom column renderers
        $this->columnRenderers = [
            'project' => function($task) {
                return '<span class="expand-btn" data-project-id="' . $task->module->project->id . '">
                            <i class="fas fa-chevron-right"></i>
                        </span>' . e($task->module->project->name);
            },
            'module' => function($task) {
                return '<span class="expand-btn" data-module-id="' . $task->module->id . '">
                            <i class="fas fa-chevron-right"></i>
                        </span>' . e($task->module->name);
            },
            'users' => function($task) {
                $html = '';
                foreach ($task->users as $user) {
                    $html .= '<span class="user-badge" title="' . e($user->email) . '">' . e($user->name) . '</span>';
                }
                return $html;
            },
            'status' => function($task) {
                $statusClasses = [
                    'completed' => 'status-completed',
                    'in_progress' => 'status-in-progress',
                    'pending' => 'status-pending',
                    'blocked' => 'status-blocked',
                ];
                $class = $statusClasses[$task->status] ?? '';
                return '<span class="status-badge ' . $class . '">' . ucfirst(str_replace('_', ' ', $task->status)) . '</span>';
            },
            'due_date' => function($task) {
                if (!$task->due_date) {
                    return 'N/A';
                }
                
                $dueDate = \Carbon\Carbon::parse($task->due_date);
                $today = \Carbon\Carbon::today();
                
                if ($dueDate->isPast()) {
                    return '<span class="text-danger">' . $dueDate->format('M d, Y') . ' (Overdue)</span>';
                } elseif ($dueDate->isToday()) {
                    return '<span class="text-warning">' . $dueDate->format('M d, Y') . ' (Today)</span>';
                } elseif ($dueDate->diffInDays($today) <= 3) {
                    return '<span class="text-info">' . $dueDate->format('M d, Y') . ' (Soon)</span>';
                } else {
                    return $dueDate->format('M d, Y');
                }
            },
        ];
    }
    
    public function index(Request $request)
    {
        return $this->handleDataTable($request, 'task-manager.index');
    }
    
    protected function buildTableQuery(Request $request)
    {
        // Start with a base query that includes all the necessary joins
        $query = Task::query()
            ->join('modules', 'tasks.module_id', '=', 'modules.id')
            ->join('projects', 'modules.project_id', '=', 'projects.id')
            ->leftJoin('task_user', 'tasks.id', '=', 'task_user.task_id')
            ->leftJoin('users', 'task_user.user_id', '=', 'users.id')
            ->select('tasks.*')
            ->groupBy('tasks.id');
        
        // Apply filters
        $this->applyFilters($query, $request);
        
        // Apply sorting
        $this->applySorting($query, $request);
        
        return $query;
    }
    
    protected function getFilterOptions()
    {
        return [
            'projects' => Project::pluck('name', 'id')->toArray(),
            'statuses' => [
                'completed' => 'Completed',
                'in_progress' => 'In Progress',
                'pending' => 'Pending',
                'blocked' => 'Blocked',
            ],
            'users' => User::pluck('name', 'id')->toArray(),
        ];
    }
}</code></pre>
        </div>
        
        <div class="section-card">
            <h2>View</h2>
            <p>Here's the Blade view for our task manager:</p>
            
            <pre><code>@extends('layouts.app')

@section('content')
    &lt;div class="container"&gt;
        &lt;h1&gt;Task Manager&lt;/h1&gt;
        
        &lt;x-data-table
            :items="$items"
            :columns="[
                'id' => 'ID',
                'project' => 'Project',
                'module' => 'Module',
                'name' => 'Task',
                'users' => 'Assigned To',
                'status' => 'Status',
                'due_date' => 'Due Date',
            ]"
            :sortable="['id', 'name', 'status', 'due_date']"
            :filter-options="$filterOptions"
            title="Tasks"
            route-name="tasks.index"
        /&gt;
    &lt;/div&gt;
    
    &lt;script&gt;
        // JavaScript to handle expanding/collapsing projects and modules
        $(document).ready(function() {
            // Handle project expand/collapse
            $(document).on('click', '.expand-btn[data-project-id]', function() {
                const projectId = $(this).data('project-id');
                const icon = $(this).find('i');
                
                if (icon.hasClass('fa-chevron-right')) {
                    // Expand
                    icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                    
                    // Show all tasks for this project
                    $('.project-row[data-project-id="' + projectId + '"]').show();
                    $('.module-row[data-project-id="' + projectId + '"]').show();
                } else {
                    // Collapse
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                    
                    // Hide all tasks for this project
                    $('.project-row[data-project-id="' + projectId + '"]').hide();
                    $('.module-row[data-project-id="' + projectId + '"]').hide();
                    $('.task-row[data-project-id="' + projectId + '"]').hide();
                    
                    // Reset module icons
                    $('.expand-btn[data-module-id]').find('i')
                        .removeClass('fa-chevron-down')
                        .addClass('fa-chevron-right');
                }
            });
            
            // Handle module expand/collapse
            $(document).on('click', '.expand-btn[data-module-id]', function() {
                const moduleId = $(this).data('module-id');
                const icon = $(this).find('i');
                
                if (icon.hasClass('fa-chevron-right')) {
                    // Expand
                    icon.removeClass('fa-chevron-right').addClass('fa-chevron-down');
                    
                    // Show all tasks for this module
                    $('.task-row[data-module-id="' + moduleId + '"]').show();
                } else {
                    // Collapse
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-right');
                    
                    // Hide all tasks for this module
                    $('.task-row[data-module-id="' + moduleId + '"]').hide();
                }
            });
        });
    &lt;/script&gt;
@endsection</code></pre>
        </div>
        
        <div class="section-card">
            <h2>Custom Table Rows Template</h2>
            <p>Here's the custom table rows template that displays the hierarchical structure:</p>
            
            <pre><code>@foreach($items->groupBy(function($task) { return $task->module->project->id; }) as $projectId => $projectTasks)
    @php
        $project = $projectTasks->first()->module->project;
    @endphp
    
    &lt;!-- Project Row --&gt;
    &lt;tr class="project-row" data-project-id="{{ $project->id }}"&gt;
        &lt;td&gt;{{ $project->id }}&lt;/td&gt;
        &lt;td&gt;
            &lt;span class="expand-btn" data-project-id="{{ $project->id }}"&gt;
                &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
            &lt;/span&gt;
            &lt;strong&gt;{{ $project->name }}&lt;/strong&gt;
        &lt;/td&gt;
        &lt;td colspan="5"&gt;
            &lt;span class="text-muted"&gt;
                {{ $project->start_date->format('M d, Y') }} - {{ $project->end_date->format('M d, Y') }}
            &lt;/span&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
    
    @foreach($projectTasks->groupBy(function($task) { return $task->module->id; }) as $moduleId => $moduleTasks)
        @php
            $module = $moduleTasks->first()->module;
        @endphp
        
        &lt;!-- Module Row --&gt;
        &lt;tr class="module-row" data-project-id="{{ $project->id }}" data-module-id="{{ $module->id }}" style="display: none;"&gt;
            &lt;td&gt;{{ $module->id }}&lt;/td&gt;
            &lt;td&gt;&lt;/td&gt;
            &lt;td class="indent-1"&gt;
                &lt;span class="expand-btn" data-module-id="{{ $module->id }}"&gt;
                    &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
                &lt;/span&gt;
                &lt;strong&gt;{{ $module->name }}&lt;/strong&gt;
            &lt;/td&gt;
            &lt;td colspan="4"&gt;
                &lt;span class="text-muted"&gt;{{ $module->description }}&lt;/span&gt;
            &lt;/td&gt;
        &lt;/tr&gt;
        
        @foreach($moduleTasks as $task)
            &lt;!-- Task Row --&gt;
            &lt;tr class="task-row" data-project-id="{{ $project->id }}" data-module-id="{{ $module->id }}" style="display: none;"&gt;
                &lt;td&gt;{{ $task->id }}&lt;/td&gt;
                &lt;td&gt;&lt;/td&gt;
                &lt;td&gt;&lt;/td&gt;
                &lt;td class="indent-2"&gt;{{ $task->name }}&lt;/td&gt;
                &lt;td&gt;
                    @foreach($task->users as $user)
                        &lt;span class="user-badge" title="{{ $user->email }}"&gt;{{ $user->name }}&lt;/span&gt;
                    @endforeach
                &lt;/td&gt;
                &lt;td&gt;
                    @php
                        $statusClasses = [
                            'completed' => 'status-completed',
                            'in_progress' => 'status-in-progress',
                            'pending' => 'status-pending',
                            'blocked' => 'status-blocked',
                        ];
                        $class = $statusClasses[$task->status] ?? '';
                    @endphp
                    &lt;span class="status-badge {{ $class }}"&gt;{{ ucfirst(str_replace('_', ' ', $task->status)) }}&lt;/span&gt;
                &lt;/td&gt;
                &lt;td&gt;
                    @if($task->due_date)
                        @php
                            $dueDate = \Carbon\Carbon::parse($task->due_date);
                            $today = \Carbon\Carbon::today();
                            
                            if ($dueDate->isPast()) {
                                echo '&lt;span class="text-danger"&gt;' . $dueDate->format('M d, Y') . ' (Overdue)&lt;/span&gt;';
                            } elseif ($dueDate->isToday()) {
                                echo '&lt;span class="text-warning"&gt;' . $dueDate->format('M d, Y') . ' (Today)&lt;/span&gt;';
                            } elseif ($dueDate->diffInDays($today) <= 3) {
                                echo '&lt;span class="text-info"&gt;' . $dueDate->format('M d, Y') . ' (Soon)&lt;/span&gt;';
                            } else {
                                echo $dueDate->format('M d, Y');
                            }
                        @endphp
                    @else
                        N/A
                    @endif
                &lt;/td&gt;
            &lt;/tr&gt;
        @endforeach
    @endforeach
@endforeach</code></pre>
        </div>
        
        <div class="section-card">
            <h2>Table Preview</h2>
            <p>Here's a preview of how the task manager table would look:</p>
            
            <div class="table-preview">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Project</th>
                            <th>Module</th>
                            <th>Task</th>
                            <th>Assigned To</th>
                            <th>Status</th>
                            <th>Due Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Project 1 -->
                        <tr class="project-row">
                            <td>1</td>
                            <td>
                                <span class="expand-btn">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                                <strong>Website Redesign</strong>
                            </td>
                            <td colspan="5">
                                <span class="text-muted">
                                    Jan 15, 2023 - Jun 30, 2023
                                </span>
                            </td>
                        </tr>
                        
                        <!-- Module 1.1 -->
                        <tr class="module-row">
                            <td>1</td>
                            <td></td>
                            <td class="indent-1">
                                <span class="expand-btn">
                                    <i class="fas fa-chevron-down"></i>
                                </span>
                                <strong>Frontend Development</strong>
                            </td>
                            <td colspan="4">
                                <span class="text-muted">UI/UX implementation for the website</span>
                            </td>
                        </tr>
                        
                        <!-- Tasks for Module 1.1 -->
                        <tr class="task-row">
                            <td>1</td>
                            <td></td>
                            <td></td>
                            <td class="indent-2">Design homepage mockup</td>
                            <td>
                                <span class="user-badge">John Doe</span>
                                <span class="user-badge">Jane Smith</span>
                            </td>
                            <td>
                                <span class="status-badge status-completed">Completed</span>
                            </td>
                            <td>Feb 15, 2023</td>
                        </tr>
                        <tr class="task-row">
                            <td>2</td>
                            <td></td>
                            <td></td>
                            <td class="indent-2">Implement responsive layout</td>
                            <td>
                                <span class="user-badge">John Doe</span>
                            </td>
                            <td>
                                <span class="status-badge status-in-progress">In Progress</span>
                            </td>
                            <td><span class="text-danger">Mar 20, 2023 (Overdue)</span></td>
                        </tr>
                        
                        <!-- Module 1.2 -->
                        <tr class="module-row">
                            <td>2</td>
                            <td></td>
                            <td class="indent-1">
                                <span class="expand-btn">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                                <strong>Backend Development</strong>
                            </td>
                            <td colspan="4">
                                <span class="text-muted">API and database implementation</span>
                            </td>
                        </tr>
                        
                        <!-- Project 2 -->
                        <tr class="project-row">
                            <td>2</td>
                            <td>
                                <span class="expand-btn">
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                                <strong>Mobile App Development</strong>
                            </td>
                            <td colspan="5">
                                <span class="text-muted">
                                    Mar 1, 2023 - Dec 31, 2023
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="section-card">
            <h2>Key Features of This Implementation</h2>
            <ul>
                <li><strong>Hierarchical Data Display</strong> - Projects, modules, and tasks are displayed in a hierarchical structure</li>
                <li><strong>Expandable/Collapsible Sections</strong> - Users can expand or collapse projects and modules</li>
                <li><strong>Multiple User Assignments</strong> - Tasks can be assigned to multiple users, displayed as badges</li>
                <li><strong>Status Indicators</strong> - Visual indicators for different task statuses</li>
                <li><strong>Due Date Formatting</strong> - Special formatting for overdue, today, and upcoming due dates</li>
                <li><strong>Advanced Filtering</strong> - Filter by project, status, assigned user, and due date range</li>
            </ul>
        </div>
        
        <div class="section-card">
            <h2>Conclusion</h2>
            <p>This example demonstrates how to use the Dynamic Table component to create a complex task manager with hierarchical data and multiple relationships. The key aspects of this implementation are:</p>
            <ol>
                <li>Custom query building with joins to handle the relationships between projects, modules, tasks, and users</li>
                <li>Custom column renderers to display complex data like user assignments and statuses</li>
                <li>Custom table rows template to display the hierarchical structure</li>
                <li>JavaScript to handle expanding and collapsing the hierarchical view</li>
            </ol>
            <p>This approach can be adapted to other hierarchical data structures and complex relationships in your own applications.</p>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
</body>
</html>
