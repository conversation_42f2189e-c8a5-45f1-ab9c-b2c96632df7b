import axios from "axios";
import { defineStore } from "pinia";

export const useMessageStore = defineStore('messages', {
    state: () => ({
        page: 1,
        messages: [],
        isLoaded: false,
    }),
    getters: {
        allmessages: (state) => {
            return state.messages;
        },
        getLoaded: (state) => {
            return state.isLoaded;
        }
    },
    actions: {
        fetchMessages(roomslug, page = 1) {
            console.log(roomslug);
            axios.get(`/room/${roomslug}/messages?page=${page}`)
                .then((response) => {
                    this.messages = [...this.messages, ...response.data.data];
                    this.page = response.data.meta.current_page;
                    this.isLoaded = true;
                })
        },
        fetchMoreMessages(roomslug) {
            this.fetchMessages(roomslug, this.page + 1);
        },
        sendstoreMessage(roomslug, message) {
            axios.post(`/room/${roomslug}/messages`, message)
                .then((response) => {
                    this.messages.push(response.data);
                })
                .catch(error => {
                    console.error("Error sending message: ", error);
                });
        }
    },
});