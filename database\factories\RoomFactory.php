<?php

namespace Database\Factories;

use App\Models\Room;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Room>
 */
class RoomFactory extends Factory
{
    protected $model = Room::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(3),  // Random 3-word title

            'slug' => $this->faker->slug, // Generate a random slug
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
}
