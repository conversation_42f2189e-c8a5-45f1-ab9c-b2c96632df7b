<?php

namespace App\Http\Controllers;

use App\Events\MessageCreated;
use App\Http\Requests\StoreMessageRequest;
use App\Http\Resources\MessageResource;
use App\Models\Message;
use App\Models\Room;
use Illuminate\Http\Request;

class MessageController extends Controller
{
    public function index(Room $room)
    {
        $messages = Message::query()
            ->select('messages.*', 'rooms.title', 'users.name')
            ->join('rooms', 'messages.room_id', '=', 'rooms.id')
            ->join('users', 'messages.user_id', '=', 'users.id')
            ->where('room_id', $room->id)
            ->paginate(20);


        return MessageResource::collection($messages);
        // $room->messages();
        // $room->messages()->with('user')->get();

        // dd($room);
    }

    public function store(StoreMessageRequest $request, Room $room)
    {
        // Another way to create a message
        // dd($request->all());
        // $message = $room->messages()->make($request->validated());
        // $message->user()->associate(auth()->user());
        // $message->save();

        $message = new Message();
        $message->content = $request->content;
        $message->user_id = auth()->user()->id;
        $message->room_id = $room->id;
        $message->save();
        broadcast(new MessageCreated($message))->toOthers();
        
        logger('Broadcasting MessageCreated:', ['message_id' => $message->id]);


        return MessageResource::make($message);

        // dd($message);
    }
}
