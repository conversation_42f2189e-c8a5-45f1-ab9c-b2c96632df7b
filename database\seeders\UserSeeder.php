<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Carbon\Carbon; // Import Carbon for handling dates

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now(); // Get the current timestamp

        // Fake user data with timestamps
        $users = [
            [
                'name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'created_at' => $now,
                'updated_at' => $now,
            ],
            [
                'name' => 'User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // Bulk insert users
        User::insert($users);
    }
}
