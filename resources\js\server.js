// import http from 'http';
// import { WebSocketServer } from 'ws';

// // Create an HTTP server
// const server = http.createServer((req, res) => {
//     res.writeHead(200, { 'Content-Type': 'text/plain' });
//     res.end('This is an HTTP server fallback.\n');
// });

// const wss = new WebSocketServer({ server });

// // Handle WebSocket connections
// wss.on('connection', (ws) => {
//     console.log('A client connected!');

//     // Listen for messages from the client
//     ws.on('message', (message) => {
//         console.log('got an Message');
//         // Echo the received message back to the client
//         ws.send(`Server echo: ${message}`);
//     });

// });

// // Start the HTTP server
// server.listen(9096, () => {
//     console.log('Server is running on http://127.0.0.1:9096');
// });
