<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ProductController extends Controller
{
    // public function index(Request $request)
    // {

    //     //return in form of api response
    //     //check if page is provied case for limit and page
    //     $limit = $request->input('limit', 5);
    //     $products = Product::paginate($limit);
    //     return response()->json($products);

    //     // Check if this is an AJAX request from DataTables
    //     // if ($request->ajax()) {
    //     //     $products = Product::query();

    //     //     return DataTables::of($products)
    //     //         // ->addColumn('image', function ($product) {
    //     //         //     return '<img src="'.$product->image_url.'" alt="'.$product->name.'" class="product-image">';
    //     //         // })
    //     //         ->addColumn('status', function ($product) {
    //     //             return '<span class="status-badge status-'.$product->status.'">'.
    //     //                 ucfirst(str_replace('_', ' ', $product->status)).'</span>';
    //     //         })
    //     //         ->rawColumns(['status'])
    //     //         ->make(true);
    //     // }

    //     // // For non-AJAX requests, return the view
    //     // return view('product-index');
    // }
    public function index(Request $request)
{
    $perPage = $request->get('per_page', 5); // Default to 5 if not provided
    $page = $request->get('page', 1);
    
    $query = Product::query();
    
    // Handle filtering
    if ($request->has('filter')) {
        foreach ($request->get('filter') as $field => $value) {
            if (!empty($value)) {
                $query->where($field, 'like', '%' . $value . '%');
            }
        }
    }
    
    // Handle sorting
    if ($request->has('sort')) {
        $sortField = $request->get('sort');
        $sortDirection = $request->get('direction', 'asc');
        $query->orderBy($sortField, $sortDirection);
    }
    
    // Paginate with the requested per_page value
    return $query->paginate($perPage, ['*'], 'page', $page);
}
}

