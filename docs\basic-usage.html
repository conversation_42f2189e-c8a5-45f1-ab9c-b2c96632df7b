<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Usage - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Basic Usage</h1>
            <p class="lead">Learn how to use the Dynamic Table component in your Laravel project.</p>
            
            <div class="section-card">
                <h2>Creating a Controller</h2>
                <p>First, create a controller that uses the <code>HasDataTable</code> trait:</p>
                <pre><code>namespace App\Http\Controllers;

use App\Exports\YourExportClass;
use App\Models\YourModel;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class YourTableController extends Controller
{
    use HasDataTable;
    
    public function __construct()
    {
        $this->tableModel = YourModel::class;
        $this->exportClass = YourExportClass::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ];
        $this->sortableColumns = ['id', 'name'];
        
        // Optional: customize filter fields
        $this->filterFields = [
            'search' => ['name', 'description'],
            'category' => 'category',
            // Add your filter fields here
        ];
    }
    
    public function index(Request $request)
    {
        return $this->handleDataTable($request, 'your-view-path');
    }
}</code></pre>
                <p>The <code>HasDataTable</code> trait provides all the functionality needed for the table component, including:</p>
                <ul>
                    <li>Building the query based on filters, sorting, and pagination</li>
                    <li>Handling AJAX requests for refresh-free operations</li>
                    <li>Exporting data to Excel, CSV, and PDF formats</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Creating a Route</h2>
                <p>Add a route for your table controller:</p>
                <pre><code>Route::get('/your-table', [YourTableController::class, 'index'])->name('your-table.index');</code></pre>
                <p>Make sure the route name matches the <code>route-name</code> parameter in your table component.</p>
            </div>
            
            <div class="section-card">
                <h2>Creating a View</h2>
                <p>Create a view file and add the table component:</p>
                <pre><code>@extends('your-layout')

@section('content')
    &lt;x-data-table
        :items="$items"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ]"
        :sortable="['id', 'name']"
        :filter-options="$filterOptions"
        title="Your Table Title"
        route-name="your-table.index"
    /&gt;
@endsection</code></pre>
                <p>The component accepts the following props:</p>
                <ul>
                    <li><code>items</code>: The data to display in the table (provided by the controller)</li>
                    <li><code>columns</code>: The columns to display in the table</li>
                    <li><code>sortable</code>: The columns that can be sorted</li>
                    <li><code>filter-options</code>: The filter options for the table (provided by the controller)</li>
                    <li><code>title</code>: The title of the table</li>
                    <li><code>route-name</code>: The route name for table actions</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Using the Lazy Loading Table</h2>
                <p>To use the lazy loading version of the table:</p>
                <pre><code>// In your controller
public function index(Request $request)
{
    // Add lazy_load parameter to request
    $request->merge(['lazy_load' => true]);
    
    return $this->handleDataTable($request, 'your-lazy-view-path');
}

// In your view
@extends('your-layout')

@section('content')
    &lt;x-lazy-table
        :items="$items"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ]"
        :sortable="['id', 'name']"
        :filter-options="$filterOptions"
        :total-count="$totalCount"
        :has-more-pages="$hasMorePages"
        :next-page="$nextPage"
        title="Your Lazy Loading Table"
        route-name="your-table.index"
    /&gt;
@endsection</code></pre>
                <p>The lazy loading table automatically loads more data when the user scrolls to the bottom of the table.</p>
            </div>
            
            <div class="section-card">
                <h2>Component Props</h2>
                <h3>Standard Table Props</h3>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Prop</th>
                            <th>Type</th>
                            <th>Default</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>items</code></td>
                            <td>Collection</td>
                            <td>Required</td>
                            <td>The data to display in the table</td>
                        </tr>
                        <tr>
                            <td><code>columns</code></td>
                            <td>Array</td>
                            <td>Required</td>
                            <td>The columns to display in the table</td>
                        </tr>
                        <tr>
                            <td><code>sortable</code></td>
                            <td>Array</td>
                            <td><code>[]</code></td>
                            <td>The columns that can be sorted</td>
                        </tr>
                        <tr>
                            <td><code>searchable</code></td>
                            <td>Boolean</td>
                            <td><code>true</code></td>
                            <td>Whether to enable search</td>
                        </tr>
                        <tr>
                            <td><code>filterable</code></td>
                            <td>Boolean</td>
                            <td><code>true</code></td>
                            <td>Whether to enable filters</td>
                        </tr>
                        <tr>
                            <td><code>pagination</code></td>
                            <td>Boolean</td>
                            <td><code>true</code></td>
                            <td>Whether to enable pagination</td>
                        </tr>
                        <tr>
                            <td><code>exportable</code></td>
                            <td>Boolean</td>
                            <td><code>true</code></td>
                            <td>Whether to enable export functionality</td>
                        </tr>
                        <tr>
                            <td><code>filter-options</code></td>
                            <td>Array</td>
                            <td><code>[]</code></td>
                            <td>Filter options for the table</td>
                        </tr>
                        <tr>
                            <td><code>title</code></td>
                            <td>String</td>
                            <td><code>'Data Table'</code></td>
                            <td>The title of the table</td>
                        </tr>
                        <tr>
                            <td><code>route-name</code></td>
                            <td>String</td>
                            <td><code>'data-table.index'</code></td>
                            <td>The route name for table actions</td>
                        </tr>
                    </tbody>
                </table>
                
                <h3>Lazy Table Additional Props</h3>
                <table class="table table-bordered mt-3">
                    <thead>
                        <tr>
                            <th>Prop</th>
                            <th>Type</th>
                            <th>Default</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><code>total-count</code></td>
                            <td>Integer</td>
                            <td><code>0</code></td>
                            <td>Total number of items</td>
                        </tr>
                        <tr>
                            <td><code>has-more-pages</code></td>
                            <td>Boolean</td>
                            <td><code>false</code></td>
                            <td>Whether there are more pages to load</td>
                        </tr>
                        <tr>
                            <td><code>next-page</code></td>
                            <td>Integer</td>
                            <td><code>2</code></td>
                            <td>The next page number to load</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the filtering feature:</p>
                <a href="filtering.html" class="btn btn-primary">Filtering Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
