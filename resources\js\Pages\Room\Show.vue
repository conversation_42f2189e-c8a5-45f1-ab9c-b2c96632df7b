<script setup>
import Footer from '@/Components/Chat/Footer.vue';
import Header from '@/Components/Chat/Header.vue';
import Messages from '@/Components/Chat/Messages.vue';
import Nav from '@/Components/Chat/Nav.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { useMessageStore } from '@/Store/useMessageStore';
import { Head } from '@inertiajs/vue3';
import { onMounted } from 'vue';

const props = defineProps({
    room: {
        type: Object,
        required: true
    }
});

const messageStore = useMessageStore();
messageStore.fetchMessages(props.room.slug);

//child functions
const storeMessage = (payload) => {
    messageStore.sendstoreMessage(props.room.slug, payload);
};

// const channel = Echo.join(`room.${props.room.slug}`);
// console.log(channel);

// channel.listen('MessageCreated', (e) => { console.log(e); });

//Websocket setup
let server = new WebSocket('ws://localhost:9096/socket.io');

server.onopen = (event) => {
    console.log('Connected to server');
};  

</script>

<template>

    <Head title="Dashboard" />

    <head title="Messages" />
    <div>
        <!-- Page Container -->
        <div id="page-container" class="relative mx-auto h-screen min-w-[320px] bg-white lg:ms-80">
            <!-- Page Sidebar -->
            <Nav />
            <!-- Page Sidebar -->

            <!-- Page Header -->
            <Header />
            <!-- END Page Header -->

            <!-- Page Content -->
            <Messages :room="room" />
            <!-- END Page Content -->

            <!-- Page Footer -->
            <Footer @valid="storeMessage({ content: $event })" />
            <!-- END Page Footer -->
        </div>
        <!-- END Page Container -->
    </div>
</template>
