<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Lazy Loading Table</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Styles -->
    <style>
        :root {
            --primary-color: #4f46e5;
            --primary-hover: #4338ca;
            --secondary-color: #6b7280;
            --light-gray: #f3f4f6;
            --medium-gray: #e5e7eb;
            --dark-gray: #374151;
            --danger-color: #ef4444;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --border-radius: 0.375rem;
            --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Figtree', sans-serif;
            background-color: #f9fafb;
            color: #111827;
            line-height: 1.5;
        }

        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 1rem;
        }

        /* Dynamic Table Container */
        .dynamic-table-container {
            position: relative;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1.5rem;
            overflow: hidden;
        }

        /* Filter Sidebar */
        .filter-sidebar {
            position: fixed;
            top: 0;
            left: -320px;
            width: 320px;
            height: 100vh;
            background-color: white;
            box-shadow: var(--box-shadow);
            z-index: 1000;
            padding: 1.5rem;
            overflow-y: auto;
            transition: var(--transition);
        }

        .filter-sidebar.active {
            left: 0;
        }

        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid var(--medium-gray);
        }

        .filter-header h3 {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .close-filters {
            background: none;
            border: none;
            font-size: 1.25rem;
            cursor: pointer;
            color: var(--secondary-color);
        }

        .filter-section {
            margin-bottom: 1.5rem;
        }

        .filter-section h4 {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .filter-input {
            margin-bottom: 0.75rem;
        }

        .filter-input input,
        .filter-input select {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
        }

        .filter-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1.5rem;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            font-size: 0.875rem;
        }

        .btn-apply {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-apply:hover {
            background-color: var(--primary-hover);
        }

        .btn-reset {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-reset:hover {
            background-color: var(--medium-gray);
        }

        .btn-filter {
            background-color: var(--light-gray);
            color: var(--dark-gray);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-filter:hover {
            background-color: var(--medium-gray);
        }

        /* Table Content */
        .table-content {
            padding: 1.5rem;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .table-header h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .table-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .search-container {
            position: relative;
        }

        .quick-search {
            padding: 0.5rem 2.5rem 0.5rem 0.75rem;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            font-size: 0.875rem;
            width: 200px;
        }

        #quickSearchBtn {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--secondary-color);
            cursor: pointer;
        }

        /* Table Styles */
        .table-responsive {
            overflow-x: auto;
            max-height: 70vh; /* Limit height to 70% of viewport height */
            overflow-y: auto; /* Enable vertical scrolling */
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            text-align: left;
        }

        .data-table th,
        .data-table td {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--medium-gray);
        }

        .data-table th {
            background-color: var(--light-gray);
            font-weight: 600;
            font-size: 0.875rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table tbody tr:hover {
            background-color: rgba(243, 244, 246, 0.5);
        }

        .sortable {
            cursor: pointer;
        }

        .sort-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: inherit;
            text-decoration: none;
        }

        /* Status Badges */
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-active {
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-inactive {
            background-color: rgba(107, 114, 128, 0.1);
            color: var(--secondary-color);
        }

        .status-out_of_stock {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .status-pending {
            background-color: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        /* Image */
        .item-image {
            width: 50px;
            height: 50px;
            overflow: hidden;
            border-radius: var(--border-radius);
        }

        .item-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-view,
        .btn-edit,
        .btn-delete {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            padding: 0.25rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .btn-view {
            color: var(--info-color);
        }

        .btn-edit {
            color: var(--warning-color);
        }

        .btn-delete {
            color: var(--danger-color);
        }

        .btn-view:hover,
        .btn-edit:hover,
        .btn-delete:hover {
            background-color: var(--light-gray);
        }

        /* Table Footer */
        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 1.5rem;
            border-top: 1px solid var(--medium-gray);
            margin-top: 1.5rem;
        }

        /* Lazy Loading Styles */
        .lazy-load-status {
            font-size: 0.875rem;
            color: var(--secondary-color);
        }

        .loading-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: var(--primary-color);
        }

        .loading-complete {
            color: var(--success-color);
        }

        .loading-info {
            margin-top: 0.5rem;
        }

        .per-page-options {
            font-size: 0.875rem;
            color: var(--secondary-color);
        }

        .per-page-options select {
            padding: 0.25rem 0.5rem;
            border: 1px solid var(--medium-gray);
            border-radius: var(--border-radius);
            margin: 0 0.25rem;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .table-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 1rem;
            }

            .table-actions {
                width: 100%;
                justify-content: space-between;
            }

            .quick-search {
                width: 150px;
            }

            .table-footer {
                flex-direction: column;
                gap: 1rem;
                align-items: flex-start;
            }

            .per-page-options {
                align-self: flex-end;
            }
        }

        /* Overlay */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            display: none;
        }

        .overlay.active {
            display: block;
        }

        /* Export Buttons */
        .export-buttons {
            position: relative;
        }

        .btn-export {
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-export:hover {
            background-color: var(--primary-hover);
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 100;
            min-width: 160px;
            padding: 0.5rem 0;
            margin-top: 0.25rem;
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            display: none;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            color: var(--dark-gray);
            text-decoration: none;
            transition: var(--transition);
        }

        .dropdown-item:hover {
            background-color: var(--light-gray);
        }
    </style>
</head>
<body>
    <div class="container">
        @yield('content')
    </div>

    <div class="overlay" id="overlay"></div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ asset('js/dynamic-table.js') }}"></script>
    <script>
        /*
        // Old script - replaced by dynamic-table.js
        document.addEventListener('DOMContentLoaded', function() {
            const filterSidebar = document.getElementById('filterSidebar');
            const showFiltersBtn = document.getElementById('showFilters');
            const closeFiltersBtn = document.getElementById('closeFilters');
            const overlay = document.getElementById('overlay');
            const tableBody = document.getElementById('tableBody');
            const tableResponsive = document.querySelector('.table-responsive');
            const quickSearch = document.getElementById('quickSearch');
            const quickSearchBtn = document.getElementById('quickSearchBtn');
            const itemsPerPage = document.getElementById('itemsPerPage');
            const filterForm = document.getElementById('filterForm');
            const lazyTable = document.getElementById('lazyTable');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadingComplete = document.getElementById('loadingComplete');
            const currentCountElement = document.getElementById('currentCount');
            const totalCountElement = document.getElementById('totalCount');
            const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            let isLoading = false;
            let nextPage = parseInt(lazyTable.dataset.nextPage);
            let hasMore = lazyTable.dataset.hasMore === 'true';

            // Toggle filter sidebar
            showFiltersBtn.addEventListener('click', function() {
                filterSidebar.classList.add('active');
                overlay.classList.add('active');
            });

            closeFiltersBtn.addEventListener('click', function() {
                filterSidebar.classList.remove('active');
                overlay.classList.remove('active');
            });

            overlay.addEventListener('click', function() {
                filterSidebar.classList.remove('active');
                overlay.classList.remove('active');
            });

            // Handle sort clicks
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('sort-link') || e.target.parentElement.classList.contains('sort-link')) {
                    e.preventDefault();
                    const link = e.target.classList.contains('sort-link') ? e.target : e.target.parentElement;
                    const url = link.getAttribute('href');
                    if (url) {
                        window.location.href = url;
                    }
                }
            });

            // Handle quick search
            quickSearchBtn.addEventListener('click', function() {
                const searchValue = quickSearch.value.trim();
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('search', searchValue);
                // Reset to page 1 when searching
                currentUrl.searchParams.set('page', '1');
                window.location.href = currentUrl.toString();
            });

            quickSearch.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    quickSearchBtn.click();
                }
            });

            // Handle items per page change
            itemsPerPage.addEventListener('change', function() {
                const perPage = this.value;
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('per_page', perPage);
                // Reset to page 1 when changing per page size
                currentUrl.searchParams.set('page', '1');
                window.location.href = currentUrl.toString();
            });

            // Handle filter form submission
            if (filterForm) {
                filterForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    // Ensure we're starting at page 1 when applying filters
                    formData.append('page', '1');
                    const queryString = new URLSearchParams(formData).toString();
                    const url = `${this.action}?${queryString}`;
                    window.location.href = url;
                    filterSidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }

            // Implement lazy loading
            function loadMoreItems() {
                if (isLoading || !hasMore) return;

                isLoading = true;
                loadingIndicator.style.display = 'flex';

                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('page', nextPage);

                fetch(currentUrl.toString(), {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Append new rows to the table
                    tableBody.insertAdjacentHTML('beforeend', data.html);

                    // Update counters and status
                    currentCountElement.textContent = data.currentCount;
                    totalCountElement.textContent = data.totalCount;

                    // Update pagination state
                    hasMore = data.hasMorePages;
                    nextPage = data.nextPage;

                    // Update UI
                    loadingIndicator.style.display = 'none';
                    if (!hasMore) {
                        loadingComplete.style.display = 'block';
                    }

                    isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading more items:', error);
                    loadingIndicator.style.display = 'none';
                    isLoading = false;
                });
            }

            // Detect when user scrolls to bottom of table
            if (tableResponsive) {
                tableResponsive.addEventListener('scroll', function() {
                    // Check if we're near the bottom of the scrollable area
                    const scrollPosition = this.scrollTop + this.clientHeight;
                    const scrollHeight = this.scrollHeight;

                    // Load more when within 100px of the bottom
                    if (scrollHeight - scrollPosition < 100 && hasMore && !isLoading) {
                        loadMoreItems();
                    }
                });
            }

            // Handle export dropdown
            const exportDropdown = document.getElementById('exportDropdown');
            const exportMenu = document.getElementById('exportMenu');

            if (exportDropdown) {
                exportDropdown.addEventListener('click', function(e) {
                    e.stopPropagation();
                    exportMenu.classList.toggle('show');
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!exportDropdown.contains(e.target)) {
                        exportMenu.classList.remove('show');
                    }
                });
            }

            // Handle action buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-view')) {
                    const id = e.target.closest('.btn-view').dataset.id;
                    alert(`View item ${id}`);
                    // Implement your view logic here
                }

                if (e.target.closest('.btn-edit')) {
                    const id = e.target.closest('.btn-edit').dataset.id;
                    alert(`Edit item ${id}`);
                    // Implement your edit logic here
                }

                if (e.target.closest('.btn-delete')) {
                    const id = e.target.closest('.btn-delete').dataset.id;
                    if (confirm(`Are you sure you want to delete item ${id}?`)) {
                        alert(`Delete item ${id}`);
                        // Implement your delete logic here
                    }
                }
            });
        // */
        });
    </script>
</body>
</html>
