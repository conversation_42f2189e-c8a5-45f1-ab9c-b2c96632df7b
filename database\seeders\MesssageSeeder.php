<?php

namespace Database\Seeders;

use App\Models\Message;
use DB;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Str;

class MesssageSeeder extends Seeder
{
    /**
     * Run the database seeds. 
     */
    public function run()
    {
        // Seed 10 messages, associating each message with an existing room and user
        Message::factory(10)->create();
    }
}
