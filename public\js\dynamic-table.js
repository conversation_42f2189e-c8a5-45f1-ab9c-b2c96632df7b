/**
 * Dynamic Table JavaScript
 *
 * This script provides functionality for the dynamic table component.
 * It uses jQuery for better compatibility and simpler AJAX handling.
 */

$(document).ready(function() {
    console.log('Dynamic Table JS loaded');

    // Initialize all dynamic tables on the page
    $('.dynamic-table-container').each(function() {
        initializeTable($(this));
    });

    // Add a global click handler for debugging sort links
    $(document).on('click', '.sort-link', function(e) {
        console.log('Sort link clicked:', $(this).attr('href'));
        console.log('Parent table:', $(this).closest('table').attr('id'));
    });

    /**
     * Initialize a table with all its functionality
     *
     * @param {jQuery} tableContainer - The table container element
     */
    function initializeTable(tableContainer) {
        const tableId = tableContainer.attr('id');
        console.log(`Initializing table: ${tableId}`);

        // Determine if this is a lazy loading table
        const isLazyLoad = tableId.startsWith('lazy-table-') || tableContainer.data('lazyLoad') === true;
        console.log(`Table ${tableId} lazy loading: ${isLazyLoad}`);

        // Get route name from data attribute
        const routeName = tableContainer.data('routeName');

        // Get CSRF token
        const csrfToken = $('meta[name="csrf-token"]').attr('content');

        // Table elements
        const elements = {
            filterSidebar: $(`#filterSidebar-${tableId}`),
            showFiltersBtn: $(`#showFilters-${tableId}`),
            closeFiltersBtn: $(`#closeFilters-${tableId}`),
            filterForm: $(`#filterForm-${tableId}`),
            tableBody: $(`#tableBody-${tableId}`),
            paginationContainer: $(`#paginationContainer-${tableId}`),
            quickSearch: $(`#quickSearch-${tableId}`),
            quickSearchBtn: $(`#quickSearchBtn-${tableId}`),
            itemsPerPage: $(`#itemsPerPage-${tableId}`),
            exportDropdown: $(`#exportDropdown-${tableId}`),
            exportMenu: $(`#exportMenu-${tableId}`),
            overlay: $('#overlay'),
            resetFiltersBtn: $(`#resetFilters-${tableId}`),
            loadingIndicator: $(`#loadingIndicator-${tableId}`),
            loadingComplete: $(`#loadingComplete-${tableId}`),
            currentCount: $(`#currentCount-${tableId}`),
            totalCount: $(`#totalCount-${tableId}`)
        };

        // State
        const state = {
            isLoading: false,
            nextPage: isLazyLoad ? parseInt(tableContainer.data('nextPage') || 2) : null,
            hasMorePages: isLazyLoad ? tableContainer.data('hasMore') === true : false
        };

        // Setup event listeners
        setupEventListeners();

        /**
         * Set up all event listeners for the table
         */
        function setupEventListeners() {
            // Filter sidebar toggle
            if (elements.showFiltersBtn.length) {
                elements.showFiltersBtn.on('click', function() {
                    toggleFilterSidebar(true);
                });
            }

            if (elements.closeFiltersBtn.length) {
                elements.closeFiltersBtn.on('click', function() {
                    toggleFilterSidebar(false);
                });
            }

            if (elements.overlay.length) {
                elements.overlay.on('click', function() {
                    toggleFilterSidebar(false);
                });
            }

            // Pagination clicks
            if (elements.paginationContainer.length) {
                $(document).on('click', `#paginationContainer-${tableId} .page-link`, function(e) {
                    e.preventDefault();
                    if (!$(this).parent().hasClass('disabled')) {
                        const url = $(this).attr('href');
                        if (url) {
                            fetchTableData(url);
                        }
                    }
                });
            }

            // Sort clicks - use document-level delegation for dynamically updated content
            $(document).on('click', `.sort-link`, function(e) {
                // Check if this sort link belongs to our table
                const tableElement = $(this).closest(`#dataTable-${tableId}`);
                if (tableElement.length) {
                    e.preventDefault();
                    e.stopPropagation();

                    const url = $(this).attr('href');
                    const column = $(this).data('column');

                    if (url) {
                        console.log(`Sort link clicked for ${tableId}, column: ${column}, URL: ${url}`);

                        // Add a timestamp parameter to prevent caching
                        const urlObj = new URL(url, window.location.origin);
                        urlObj.searchParams.set('_', Date.now());

                        fetchTableData(urlObj.toString());
                    }
                }
            });

            // Quick search
            if (elements.quickSearchBtn.length && elements.quickSearch.length) {
                // Search button click
                elements.quickSearchBtn.on('click', function() {
                    const searchValue = elements.quickSearch.val().trim();
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('search', searchValue);
                    currentUrl.searchParams.set('page', '1');
                    fetchTableData(currentUrl.toString());
                });

                // Enter key in search field
                elements.quickSearch.on('keyup', function(e) {
                    if (e.key === 'Enter') {
                        elements.quickSearchBtn.click();
                    }
                });

                // Auto-search as you type (with debounce)
                let searchTimer;
                elements.quickSearch.on('input', function() {
                    clearTimeout(searchTimer);
                    searchTimer = setTimeout(function() {
                        const searchValue = elements.quickSearch.val().trim();
                        if (searchValue.length >= 2 || searchValue.length === 0) {
                            const currentUrl = new URL(window.location.href);
                            currentUrl.searchParams.set('search', searchValue);
                            currentUrl.searchParams.set('page', '1');
                            fetchTableData(currentUrl.toString());
                        }
                    }, 500);
                });
            }

            // Items per page change
            if (elements.itemsPerPage.length) {
                elements.itemsPerPage.on('change', function() {
                    const perPage = $(this).val();
                    const currentUrl = new URL(window.location.href);
                    currentUrl.searchParams.set('per_page', perPage);
                    currentUrl.searchParams.set('page', '1');
                    fetchTableData(currentUrl.toString());
                });
            }

            // Filter form
            if (elements.filterForm.length) {
                // Form submission
                elements.filterForm.on('submit', function(e) {
                    e.preventDefault();
                    const formData = new FormData(this);
                    formData.append('page', '1');

                    const queryString = new URLSearchParams(formData).toString();
                    const url = `${elements.filterForm.attr('action')}?${queryString}`;

                    // Close sidebar when explicitly submitting the form
                    fetchTableData(url, true);
                });

                // Reset filters
                if (elements.resetFiltersBtn.length) {
                    elements.resetFiltersBtn.on('click', function() {
                        elements.filterForm[0].reset();
                        const url = elements.filterForm.attr('action');
                        fetchTableData(url);
                        toggleFilterSidebar(false);
                    });
                }

                // Auto-submit on select change - but don't close the sidebar
                elements.filterForm.find('select').on('change', function() {
                    const formData = new FormData(elements.filterForm[0]);
                    formData.append('page', '1');

                    const queryString = new URLSearchParams(formData).toString();
                    const url = `${elements.filterForm.attr('action')}?${queryString}`;

                    fetchTableData(url, false); // false means don't close sidebar
                });

                // Auto-submit on date change - but don't close the sidebar
                elements.filterForm.find('input[type="date"]').on('change', function() {
                    const formData = new FormData(elements.filterForm[0]);
                    formData.append('page', '1');

                    const queryString = new URLSearchParams(formData).toString();
                    const url = `${elements.filterForm.attr('action')}?${queryString}`;

                    fetchTableData(url, false); // false means don't close sidebar
                });

                // Auto-submit on number input (with debounce) - but don't close the sidebar
                let numberTimer;
                elements.filterForm.find('input[type="number"]').on('input', function() {
                    clearTimeout(numberTimer);
                    numberTimer = setTimeout(function() {
                        const formData = new FormData(elements.filterForm[0]);
                        formData.append('page', '1');

                        const queryString = new URLSearchParams(formData).toString();
                        const url = `${elements.filterForm.attr('action')}?${queryString}`;

                        fetchTableData(url, false); // false means don't close sidebar
                    }, 500);
                });
            }

            // Export dropdown
            if (elements.exportDropdown.length && elements.exportMenu.length) {
                elements.exportDropdown.on('click', function(e) {
                    e.stopPropagation();
                    elements.exportMenu.toggleClass('show');
                });

                $(document).on('click', function(e) {
                    if (elements.exportMenu.hasClass('show') &&
                        !elements.exportDropdown.is(e.target) &&
                        elements.exportDropdown.has(e.target).length === 0) {
                        elements.exportMenu.removeClass('show');
                    }
                });
            }

            // Lazy loading
            if (isLazyLoad && elements.tableBody.length) {
                const tableResponsive = elements.tableBody.closest('.table-responsive');
                if (tableResponsive.length) {
                    tableResponsive.on('scroll', function() {
                        const scrollPosition = $(this).scrollTop() + $(this).innerHeight();
                        const scrollHeight = $(this)[0].scrollHeight;
                        const distanceToBottom = scrollHeight - scrollPosition;

                        if (distanceToBottom < 100 && state.hasMorePages && !state.isLoading) {
                            loadMoreItems();
                        }
                    });
                }
            }
        }

        /**
         * Toggle the filter sidebar
         *
         * @param {boolean} show - Whether to show or hide the sidebar
         */
        function toggleFilterSidebar(show) {
            if (elements.filterSidebar.length) {
                if (show) {
                    elements.filterSidebar.addClass('active');
                    elements.overlay.addClass('active');
                } else {
                    elements.filterSidebar.removeClass('active');
                    elements.overlay.removeClass('active');
                }
            }
        }

        /**
         * Fetch table data via AJAX
         *
         * @param {string} url - The URL to fetch data from
         * @param {boolean} closeSidebar - Whether to close the sidebar after fetching data (default: true)
         */
        function fetchTableData(url, closeSidebar = true) {
            if (state.isLoading) return;

            state.isLoading = true;
            showLoading();

            console.log(`Fetching data from: ${url}, closeSidebar: ${closeSidebar}`);

            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function(data) {
                    console.log('Data received:', data);

                    if (!isLazyLoad) {
                        // Standard table update
                        if (elements.tableBody.length && data.table) {
                            elements.tableBody.html(data.table);
                        }

                        if (elements.paginationContainer.length && data.pagination) {
                            elements.paginationContainer.html(data.pagination);
                        }

                        // Update sort icons and links in the table header
                        if (data.sort && data.direction) {
                            console.log(`Updating sort icons and links: ${data.sort} ${data.direction}`);
                            $(`#dataTable-${tableId} th.sortable .sort-link`).each(function() {
                                const column = $(this).data('column');
                                const icon = $(this).find('i');
                                const link = $(this);

                                // Update the icon
                                if (column === data.sort) {
                                    icon.attr('class', `fas fa-sort-${data.direction === 'asc' ? 'up' : 'down'}`);

                                    // Update the href attribute to toggle the direction next time
                                    const currentUrl = new URL(link.attr('href'), window.location.origin);
                                    const nextDirection = data.direction === 'asc' ? 'desc' : 'asc';
                                    currentUrl.searchParams.set('direction', nextDirection);
                                    link.attr('href', currentUrl.pathname + currentUrl.search);

                                    console.log(`Updated sort link for ${column} to ${nextDirection}: ${link.attr('href')}`);
                                } else {
                                    icon.attr('class', 'fas fa-sort');
                                }
                            });
                        }
                    } else {
                        // For lazy loading table, we need to update the table body if it's not an append operation
                        if (elements.tableBody.length && data.table && !data.append) {
                            elements.tableBody.html(data.table);

                            // Reset lazy loading state
                            state.hasMorePages = data.hasMorePages;
                            state.nextPage = data.nextPage || 2;

                            // Update counts
                            if (elements.currentCount.length && data.currentCount) {
                                elements.currentCount.text(data.currentCount);
                            }

                            if (elements.totalCount.length && data.totalCount) {
                                elements.totalCount.text(data.totalCount);
                            }

                            // Hide "all items loaded" message when we reset the table
                            if (elements.loadingComplete.length) {
                                elements.loadingComplete.css('display', 'none');
                            }

                            // Update sort icons and links in the table header
                            if (data.sort && data.direction) {
                                console.log(`Updating sort icons and links: ${data.sort} ${data.direction}`);
                                $(`#dataTable-${tableId} th.sortable .sort-link`).each(function() {
                                    const column = $(this).data('column');
                                    const icon = $(this).find('i');
                                    const link = $(this);

                                    // Update the icon
                                    if (column === data.sort) {
                                        icon.attr('class', `fas fa-sort-${data.direction === 'asc' ? 'up' : 'down'}`);

                                        // Update the href attribute to toggle the direction next time
                                        const currentUrl = new URL(link.attr('href'), window.location.origin);
                                        const nextDirection = data.direction === 'asc' ? 'desc' : 'asc';
                                        currentUrl.searchParams.set('direction', nextDirection);
                                        link.attr('href', currentUrl.pathname + currentUrl.search);

                                        console.log(`Updated sort link for ${column} to ${nextDirection}: ${link.attr('href')}`);
                                    } else {
                                        icon.attr('class', 'fas fa-sort');
                                    }
                                });
                            }
                        }
                    }

                    // Update browser URL without refreshing
                    window.history.pushState({}, '', url);

                    // Close sidebar if requested
                    if (closeSidebar) {
                        toggleFilterSidebar(false);
                    }

                    hideLoading();
                    state.isLoading = false;
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching table data:', error);
                    hideLoading();
                    state.isLoading = false;
                }
            });
        }

        /**
         * Load more items for lazy loading
         */
        function loadMoreItems() {
            if (state.isLoading || !state.hasMorePages) return;

            state.isLoading = true;
            showLazyLoading();

            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('page', state.nextPage);
            currentUrl.searchParams.set('lazy_load', 'true');

            console.log(`Loading more items from: ${currentUrl.toString()}`);

            $.ajax({
                url: currentUrl.toString(),
                type: 'GET',
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': csrfToken
                },
                success: function(data) {
                    console.log('Lazy load data received:', data);

                    // Append new rows to the table
                    if (elements.tableBody.length && data.html) {
                        // If this is an append operation, append the rows
                        // Otherwise, replace the entire table body
                        if (data.append) {
                            console.log('Appending new rows to table body');
                            elements.tableBody.append(data.html);
                        } else if (data.table) {
                            console.log('Replacing entire table body');
                            elements.tableBody.html(data.table);
                        }
                    }

                    // Update state
                    state.hasMorePages = data.hasMorePages;
                    state.nextPage = data.nextPage;

                    // Update UI
                    hideLazyLoading();
                    updateLoadingStatus(data.currentCount, data.totalCount);

                    state.isLoading = false;

                    // Update browser URL without refreshing
                    window.history.pushState({}, '', currentUrl.toString());
                },
                error: function(xhr, status, error) {
                    console.error('Error loading more items:', error);
                    hideLazyLoading();
                    state.isLoading = false;
                }
            });
        }

        /**
         * Show loading indicator for standard table
         */
        function showLoading() {
            if (elements.tableBody.length) {
                elements.tableBody.css('opacity', '0.5');
            }
        }

        /**
         * Hide loading indicator for standard table
         */
        function hideLoading() {
            if (elements.tableBody.length) {
                elements.tableBody.css('opacity', '1');
            }
        }

        /**
         * Show loading indicator for lazy loading
         */
        function showLazyLoading() {
            if (elements.loadingIndicator.length) {
                elements.loadingIndicator.css('display', 'flex');
            }
        }

        /**
         * Hide loading indicator for lazy loading
         */
        function hideLazyLoading() {
            if (elements.loadingIndicator.length) {
                elements.loadingIndicator.css('display', 'none');
            }

            // Show "all items loaded" message if no more pages
            if (!state.hasMorePages && elements.loadingComplete.length) {
                elements.loadingComplete.css('display', 'block');
            }
        }

        /**
         * Update loading status for lazy loading
         *
         * @param {number} currentCount - Current number of items loaded
         * @param {number} totalCount - Total number of items
         */
        function updateLoadingStatus(currentCount, totalCount) {
            if (elements.currentCount.length) {
                elements.currentCount.text(currentCount);
            }

            if (elements.totalCount.length) {
                elements.totalCount.text(totalCount);
            }
        }
    }
});
