@foreach($items as $item)
<tr>
    <td>{{ $item->id }}</td>
    <td>{{ $item->name }}</td>
    <td>{{ $item->category }}</td>
    <td>${{ number_format($item->price, 2) }}</td>
    <td>{{ $item->stock }}</td>
    <td>
        <span class="status-badge status-{{ $item->status }}">
            {{ ucfirst(str_replace('_', ' ', $item->status)) }}
        </span>
    </td>
    <td>{{ $item->created_at->format('Y-m-d') }}</td>
    <td>
        <div class="item-image">
            <img src="{{ $item->image_url }}" alt="{{ $item->name }}">
        </div>
    </td>
    <td>
        <div class="action-buttons">
            <button class="btn-view" data-id="{{ $item->id }}">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn-edit" data-id="{{ $item->id }}">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn-delete" data-id="{{ $item->id }}">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    </td>
</tr>
@endforeach
