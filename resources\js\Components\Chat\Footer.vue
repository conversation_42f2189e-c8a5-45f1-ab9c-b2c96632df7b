<script setup>
import { ref, watch } from 'vue';

const message = ref("");
const shift = ref(false);

// Emit event for valid message
const emit = defineEmits(['valid']);

function handleEnter() {
    if (shift.value && message.value.length > 0) {
        message.value += "\n";
        return;
    }

    if (message.value.trim() !== "") {
        emit('valid', message.value);
        message.value = "";
    }
}

function handleKeydown(e) {
    if (e.key === "Shift") {
        shift.value = true;
    }
}

function handleKeyup(e) {
    if (e.key === "Shift") {
        shift.value = false;
    }
}
</script>

<template>
    <footer id="page-footer"
        class="fixed bottom-0 end-0 start-0 items-center border-t border-slate-200/75 bg-white lg:start-80">

        <textarea v-model="message" @keydown.enter.prevent="handleEnter" @keydown="handleKeydown" @keyup="handleKeyup"
            class="-mx-5 block w-full rounded-lg border-0 px-5 py-4 leading-6 focus:border-indigo-500 focus:ring focus:ring-indigo-500/75"
            placeholder="Type a new message and hit enter.."></textarea>
    </footer>
</template>
