<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lazy Loading - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Lazy Loading</h1>
            <p class="lead">Learn how to use the lazy loading feature of the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The lazy loading feature allows the table to load data incrementally as the user scrolls, providing a smoother experience for large datasets.
            </div>
            
            <div class="section-card">
                <h2>What is Lazy Loading?</h2>
                <p>Lazy loading is a technique that defers the loading of data until it is needed. In the context of the Dynamic Table component, it means that the table initially loads only a portion of the data, and then loads more data as the user scrolls down.</p>
                <p>Benefits of lazy loading include:</p>
                <ul>
                    <li>Faster initial page load times</li>
                    <li>Reduced server load</li>
                    <li>Smoother user experience for large datasets</li>
                    <li>Infinite scrolling capability</li>
                </ul>
                <p>The Dynamic Table component provides a dedicated lazy loading table component (<code>&lt;x-lazy-table&gt;</code>) that implements this feature.</p>
            </div>
            
            <div class="section-card">
                <h2>Using the Lazy Loading Table</h2>
                <p>To use the lazy loading table, you need to:</p>
                <ol>
                    <li>Configure your controller to handle lazy loading requests</li>
                    <li>Use the <code>&lt;x-lazy-table&gt;</code> component in your view</li>
                </ol>
                
                <h3>Controller Configuration</h3>
                <p>In your controller, you need to add the <code>lazy_load</code> parameter to the request:</p>
                <pre><code>public function index(Request $request)
{
    // Add lazy_load parameter to request
    $request->merge(['lazy_load' => true]);
    
    return $this->handleDataTable($request, 'your-lazy-view-path');
}</code></pre>
                <p>The <code>HasDataTable</code> trait will detect the <code>lazy_load</code> parameter and handle the request accordingly.</p>
                
                <h3>View Configuration</h3>
                <p>In your view, use the <code>&lt;x-lazy-table&gt;</code> component instead of the standard <code>&lt;x-data-table&gt;</code> component:</p>
                <pre><code>@extends('your-layout')

@section('content')
    &lt;x-lazy-table
        :items="$items"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ]"
        :sortable="['id', 'name']"
        :filter-options="$filterOptions"
        :total-count="$totalCount"
        :has-more-pages="$hasMorePages"
        :next-page="$nextPage"
        title="Your Lazy Loading Table"
        route-name="your-table.index"
    /&gt;
@endsection</code></pre>
                <p>The lazy loading table component requires additional props:</p>
                <ul>
                    <li><code>:total-count</code>: The total number of items in the dataset</li>
                    <li><code>:has-more-pages</code>: Whether there are more pages to load</li>
                    <li><code>:next-page</code>: The next page number to load</li>
                </ul>
                <p>These props are provided by the <code>HasDataTable</code> trait when lazy loading is enabled.</p>
            </div>
            
            <div class="section-card">
                <h2>How Lazy Loading Works</h2>
                <p>The lazy loading feature works as follows:</p>
                <ol>
                    <li>The table initially loads a batch of items (e.g., the first 10 items)</li>
                    <li>As the user scrolls down and approaches the bottom of the table, more items are loaded automatically</li>
                    <li>This process continues until all items are loaded or until the user stops scrolling</li>
                    <li>A loading indicator is displayed while new items are being loaded</li>
                    <li>When all items are loaded, a message is displayed indicating that all items have been loaded</li>
                </ol>
                <p>The lazy loading is implemented using the following components:</p>
                <ul>
                    <li>Scroll event listeners to detect when the user is near the bottom of the table</li>
                    <li>AJAX requests to fetch more data</li>
                    <li>Dynamic updating of the table content</li>
                    <li>Loading indicators and status messages</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Lazy Loading Status</h2>
                <p>The lazy loading table includes a status section in the table footer that shows:</p>
                <ul>
                    <li>The current number of items loaded</li>
                    <li>The total number of items in the dataset</li>
                    <li>A loading indicator when new items are being loaded</li>
                    <li>A message when all items have been loaded</li>
                </ul>
                <p>Example status messages:</p>
                <pre><code>// While loading
Showing 10 of 100 items (Loading more items...)

// When all items are loaded
Showing 100 of 100 items (All items loaded)</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Combining Lazy Loading with Filtering and Sorting</h2>
                <p>The lazy loading feature can be combined with filtering and sorting. When a user applies filters or sorts the table, the following happens:</p>
                <ol>
                    <li>The table is reset to show only the first batch of items that match the filters or sort order</li>
                    <li>The lazy loading process starts again from the beginning with the new filters or sort order</li>
                    <li>As the user scrolls, more items that match the filters or sort order are loaded</li>
                </ol>
                <p>This ensures that the lazy loading works correctly with filters and sorting.</p>
            </div>
            
            <div class="section-card">
                <h2>Performance Considerations</h2>
                <p>While lazy loading can improve performance for large datasets, there are some considerations to keep in mind:</p>
                <ul>
                    <li>Each batch of items requires a separate AJAX request, which can increase the number of requests to the server</li>
                    <li>The server needs to keep track of which items have already been loaded</li>
                    <li>The client needs to maintain state information about the lazy loading process</li>
                </ul>
                <p>To optimize performance:</p>
                <ul>
                    <li>Choose an appropriate batch size (default is 10 items per batch)</li>
                    <li>Use database indexes for the columns used in filtering and sorting</li>
                    <li>Consider using caching for frequently accessed data</li>
                </ul>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the exporting feature:</p>
                <a href="exporting.html" class="btn btn-primary">Exporting Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
