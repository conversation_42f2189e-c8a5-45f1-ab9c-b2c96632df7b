@extends('data-table.layout')

@section('content')
    <x-data-table.table
        :items="$items"
        :filter-options="$filterOptions ?? ['categories' => $categories ?? [], 'statuses' => ['active', 'inactive', 'out_of_stock', 'pending']]"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'stock' => 'Stock',
            'status' => 'Status',
            'created_at' => 'Date',
            'image_url' => 'Image'
        ]"
        :sortable="['id', 'name', 'category', 'price', 'stock', 'status', 'created_at']"
        :searchable="true"
        :filterable="true"
        :pagination="true"
        :lazyLoad="false"
        route-name="data-table.index"
        title="Data Table"
    />
@endsection
