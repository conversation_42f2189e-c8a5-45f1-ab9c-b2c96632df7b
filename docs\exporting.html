<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exporting - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Exporting</h1>
            <p class="lead">Learn how to use the exporting feature of the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The exporting feature allows users to export table data in various formats (Excel, CSV, PDF). This is useful for offline analysis, reporting, and data sharing.
            </div>
            
            <div class="section-card">
                <h2>Export Formats</h2>
                <p>The Dynamic Table component supports exporting data in the following formats:</p>
                <ul>
                    <li><strong>Excel (.xlsx)</strong>: Microsoft Excel format, suitable for data analysis and manipulation</li>
                    <li><strong>CSV (.csv)</strong>: Comma-separated values format, suitable for importing into various applications</li>
                    <li><strong>PDF (.pdf)</strong>: Portable Document Format, suitable for printing and sharing</li>
                </ul>
                <p>The export functionality is enabled by default. You can disable it by setting the <code>:exportable</code> prop to <code>false</code>:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    :exportable="false"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
                <p>You can also specify which export formats to include using the <code>:export-formats</code> prop:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    :export-formats="['excel', 'csv']"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Export Button</h2>
                <p>The export functionality is accessed through an "Export" button in the table header. When clicked, it shows a dropdown menu with the available export formats:</p>
                <ul>
                    <li><strong>Excel</strong>: Export to Excel (.xlsx)</li>
                    <li><strong>CSV</strong>: Export to CSV (.csv)</li>
                    <li><strong>PDF</strong>: Export to PDF (.pdf)</li>
                </ul>
                <p>Each export option opens in a new tab to prevent disrupting the current table view.</p>
            </div>
            
            <div class="section-card">
                <h2>Creating an Export Class</h2>
                <p>To enable exporting, you need to create an export class for your model. This class defines how the data should be exported in each format.</p>
                <p>Here's an example of an export class:</p>
                <pre><code>namespace App\Exports;

use App\Models\YourModel;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class YourExportClass implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request ?? request();
    }

    public function collection()
    {
        // Build your query here, similar to the controller
        $query = YourModel::query();
        
        // Apply filters based on request
        if ($this->request->has('search')) {
            $search = $this->request->input('search');
            $query->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
        }
        
        if ($this->request->has('category')) {
            $query->where('category', $this->request->input('category'));
        }
        
        if ($this->request->has('status')) {
            $query->where('status', $this->request->input('status'));
        }
        
        // Apply sorting
        if ($this->request->has('sort')) {
            $direction = $this->request->input('direction', 'asc');
            $query->orderBy($this->request->input('sort'), $direction);
        }
        
        return $query->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Category',
            'Price',
            'Status',
            'Created At',
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->name,
            $row->category,
            '$' . number_format($row->price, 2),
            ucfirst($row->status),
            $row->created_at->format('Y-m-d'),
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}</code></pre>
                <p>The export class implements several interfaces:</p>
                <ul>
                    <li><code>FromCollection</code>: Defines the data to be exported</li>
                    <li><code>WithHeadings</code>: Defines the column headings</li>
                    <li><code>WithMapping</code>: Defines how each row should be mapped to the export</li>
                    <li><code>ShouldAutoSize</code>: Automatically sizes the columns to fit the content</li>
                    <li><code>WithStyles</code>: Defines styles for the exported file (Excel only)</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Configuring the Export Class</h2>
                <p>In your controller, you need to specify the export class to use:</p>
                <pre><code>public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class;
    $this->tableColumns = [
        'id' => 'ID',
        'name' => 'Name',
        // Add your columns here
    ];
    // Rest of the constructor...
}</code></pre>
                <p>The <code>$exportClass</code> property should be set to the fully qualified class name of your export class.</p>
            </div>
            
            <div class="section-card">
                <h2>PDF Export Template</h2>
                <p>For PDF exports, you can create a custom Blade template to define the layout of the PDF. This template should be placed in the <code>resources/views/exports</code> directory.</p>
                <p>Here's an example of a PDF export template:</p>
                <pre><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;meta charset="utf-8"&gt;
    &lt;title&gt;{{ $title }}&lt;/title&gt;
    &lt;style&gt;
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 10px;
        }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;div class="header"&gt;
        &lt;h1&gt;{{ $title }}&lt;/h1&gt;
        &lt;p&gt;Generated on: {{ date('Y-m-d H:i:s') }}&lt;/p&gt;
    &lt;/div&gt;
    
    &lt;table&gt;
        &lt;thead&gt;
            &lt;tr&gt;
                @foreach($headings as $heading)
                    &lt;th&gt;{{ $heading }}&lt;/th&gt;
                @endforeach
            &lt;/tr&gt;
        &lt;/thead&gt;
        &lt;tbody&gt;
            @foreach($items as $item)
                &lt;tr&gt;
                    &lt;td&gt;{{ $item->id }}&lt;/td&gt;
                    &lt;td&gt;{{ $item->name }}&lt;/td&gt;
                    &lt;td&gt;{{ $item->category }}&lt;/td&gt;
                    &lt;td&gt;${{ number_format($item->price, 2) }}&lt;/td&gt;
                    &lt;td&gt;{{ ucfirst($item->status) }}&lt;/td&gt;
                    &lt;td&gt;{{ $item->created_at->format('Y-m-d') }}&lt;/td&gt;
                &lt;/tr&gt;
            @endforeach
        &lt;/tbody&gt;
    &lt;/table&gt;
    
    &lt;div class="footer"&gt;
        &lt;p&gt;Page {{ $page }} of {{ $pages }}&lt;/p&gt;
    &lt;/div&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>
                <p>The template has access to the following variables:</p>
                <ul>
                    <li><code>$title</code>: The title of the table</li>
                    <li><code>$headings</code>: The column headings</li>
                    <li><code>$items</code>: The data items to be exported</li>
                    <li><code>$page</code>: The current page number</li>
                    <li><code>$pages</code>: The total number of pages</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Exporting with Filters and Sorting</h2>
                <p>When a user exports the table, the current filters and sort order are applied to the exported data. This ensures that the exported data matches what the user is currently viewing in the table.</p>
                <p>For example, if a user filters the table to show only active products and sorts by name, the exported data will include only active products sorted by name.</p>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to customize the table component:</p>
                <a href="customization.html" class="btn btn-primary">Customization Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
