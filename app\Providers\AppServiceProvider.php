<?php

namespace App\Providers;

use App\View\Components\DataTable;
use App\View\Components\LazyTable;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Vite::prefetch(concurrency: 3);
        JsonResource::withoutWrapping();

        // Register components
        Blade::component('data-table', DataTable::class);
        Blade::component('lazy-table', LazyTable::class);
    }
}
