@extends('lazy-table.layout')

@section('content')
    <x-lazy-table.table
        :items="$items"
        :filter-options="$filterOptions ?? ['categories' => $categories ?? [], 'statuses' => ['active', 'inactive', 'out_of_stock', 'pending']]"
        :totalCount="$totalCount"
        :hasMorePages="$hasMorePages"
        :nextPage="$nextPage"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'stock' => 'Stock',
            'status' => 'Status',
            'created_at' => 'Date',
            'image_url' => 'Image'
        ]"
        :sortable="['id', 'name', 'category', 'price', 'stock', 'status', 'created_at']"
        :searchable="true"
        :filterable="true"
        route-name="lazy-table.index"
        title="Lazy Loading Table"
    />
@endsection
