<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>The Cosmic Lokas Visualization</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap');

        :root {
            /* Celestial colors for upper lokas */
            --param-dham-color: #ffffff;
            --shiva-puri-color: #f0f9ff;
            --bishnu-puri-color: #e0f2fe;
            --satya-loka-color: #bae6fd;
            --tapa-loka-color: #93c5fd;
            --jana-loka-color: #60a5fa;
            --mahar-loka-color: #3b82f6;
            --svar-loka-color: #2563eb;
            --bhuvar-loka-color: #1d4ed8;

            /* Earth */
            --earth-color: #059669;

            /* Netherworld colors for lower lokas */
            --atala-loka-color: #4338ca;
            --vitala-loka-color: #3730a3;
            --sutala-loka-color: #312e81;
            --talatala-loka-color: #1e1b4b;
            --mahatala-loka-color: #0f172a;
            --rasatala-loka-color: #0c0a24;
            --patala-loka-color: #030712;

            /* Hell */
            --naraka-color: #7f1d1d;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: radial-gradient(ellipse at center, #0f172a 0%, #020617 100%);
            color: white;
            overflow-x: hidden;
            margin: 0;
            padding: 0;
        }

        .cosmos-container {
            position: relative;
            min-height: 100vh;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .universe {
            position: relative;
            width: 100%;
            max-width: 1200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem 0;
            transform-style: preserve-3d;
            perspective: 1000px;
        }

        .loka {
            position: relative;
            width: 300px;
            height: 300px;
            margin: 0 auto;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-weight: bold;
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
            cursor: pointer;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform-style: preserve-3d;
            opacity: 0;
            animation: fadeIn 1s forwards;
            z-index: 1;
        }

        .loka-content {
            position: relative;
            z-index: 2;
            padding: 20px;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .loka:hover .loka-content {
            background-color: rgba(0, 0, 0, 0.4);
        }

        .loka-name {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        .loka-distance {
            font-size: 0.85rem;
            opacity: 0.8;
            margin-top: 0.5rem;
            padding: 3px 10px;
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #93c5fd;
            animation: distancePulse 3s infinite;
        }

        @keyframes distancePulse {
            0%, 100% { box-shadow: 0 0 5px rgba(147, 197, 253, 0.3); }
            50% { box-shadow: 0 0 15px rgba(147, 197, 253, 0.6); }
        }

        .loka-info {
            position: absolute;
            width: 350px;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0;
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 100;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
            left: 320px;
            transform: translateY(-50%);
        }

        .loka.active .loka-info {
            opacity: 1;
            pointer-events: auto;
        }

        .loka-info-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: #f0f9ff;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 5px;
        }

        .loka-info-description {
            font-size: 0.9rem;
            line-height: 1.5;
        }

        .distance-indicator {
            position: relative;
            width: 4px;
            background: linear-gradient(to bottom, rgba(255,255,255,0.05), rgba(255,255,255,0.3), rgba(255,255,255,0.05));
            margin: 0 auto;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            opacity: 0;
            animation: fadeIn 1s forwards;
            animation-delay: 0.5s;
            overflow: visible;
        }

        .distance-indicator::before {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            animation: pulseGlow 2s infinite;
        }

        @keyframes pulseGlow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
            50% { transform: translate(-50%, -50%) scale(1.5); opacity: 1; }
        }

        .distance-label {
            position: absolute;
            left: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 8px 15px;
            border-radius: 10px;
            font-size: 0.9rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(5px);
        }

        .distance-indicator:hover .distance-label {
            opacity: 1;
        }

        /* Special styling for the 10,000 Yojan distances */
        .distance-indicator[style*="height: 120px"]::before,
        .distance-indicator[style*="height: 150px"]::before {
            background-color: rgba(59, 130, 246, 0.7); /* Blue glow for lower loka distances */
            animation: pulseBlueGlow 2s infinite;
        }

        @keyframes pulseBlueGlow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.7; }
            50% { transform: translate(-50%, -50%) scale(1.5); opacity: 1; box-shadow: 0 0 15px rgba(59, 130, 246, 0.8); }
        }

        .distance-indicator[style*="height: 120px"] .distance-label,
        .distance-indicator[style*="height: 150px"] .distance-label {
            color: #93c5fd;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }

        /* Specific loka styles with gradients and effects */
        .param-dham {
            background: radial-gradient(circle, var(--param-dham-color) 0%, rgba(255,255,255,0.7) 100%);
            color: #000;
            animation-delay: 0.1s;
            box-shadow: 0 0 50px rgba(255, 255, 255, 0.5);
        }

        .shiva-puri {
            background: radial-gradient(circle, var(--shiva-puri-color) 0%, rgba(224,242,254,0.7) 100%);
            color: #000;
            animation-delay: 0.2s;
            box-shadow: 0 0 40px rgba(224, 242, 254, 0.3);
        }

        .bishnu-puri {
            background: radial-gradient(circle, var(--bishnu-puri-color) 0%, rgba(224,242,254,0.6) 100%);
            color: #000;
            animation-delay: 0.3s;
            box-shadow: 0 0 35px rgba(224, 242, 254, 0.2);
        }

        .satya-loka {
            background: radial-gradient(circle, var(--satya-loka-color) 0%, rgba(186,230,253,0.7) 100%);
            color: #000;
            animation-delay: 0.4s;
            box-shadow: 0 0 30px rgba(186, 230, 253, 0.2);
        }

        .tapa-loka {
            background: radial-gradient(circle, var(--tapa-loka-color) 0%, rgba(147,197,253,0.7) 100%);
            color: #000;
            animation-delay: 0.5s;
        }

        .jana-loka {
            background: radial-gradient(circle, var(--jana-loka-color) 0%, rgba(96,165,250,0.7) 100%);
            color: #000;
            animation-delay: 0.6s;
        }

        .mahar-loka {
            background: radial-gradient(circle, var(--mahar-loka-color) 0%, rgba(59,130,246,0.7) 100%);
            color: #fff;
            animation-delay: 0.7s;
        }

        .svar-loka {
            background: radial-gradient(circle, var(--svar-loka-color) 0%, rgba(37,99,235,0.7) 100%);
            color: #fff;
            animation-delay: 0.8s;
        }

        .bhuvar-loka {
            background: radial-gradient(circle, var(--bhuvar-loka-color) 0%, rgba(29,78,216,0.7) 100%);
            color: #fff;
            animation-delay: 0.9s;
        }

        .bhu-loka {
            background: radial-gradient(circle, var(--earth-color) 0%, rgba(5,150,105,0.7) 100%);
            color: #fff;
            animation-delay: 1s;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 40px rgba(5, 150, 105, 0.3);
        }

        .bhu-loka::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: url('https://images.unsplash.com/photo-1614730321146-b6fa6a46bcb4?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');
            background-size: cover;
            opacity: 0.4;
            mix-blend-mode: overlay;
        }

        .bhu-loka::after {
            content: '';
            position: absolute;
            width: 120%;
            height: 120%;
            top: -10%;
            left: -10%;
            border-radius: 50%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: earthGlow 8s infinite linear;
        }

        .atala-loka {
            background: radial-gradient(circle, var(--atala-loka-color) 0%, rgba(67,56,202,0.7) 100%);
            color: #fff;
            animation-delay: 1.1s;
        }

        .vitala-loka {
            background: radial-gradient(circle, var(--vitala-loka-color) 0%, rgba(55,48,163,0.7) 100%);
            color: #fff;
            animation-delay: 1.2s;
        }

        .sutala-loka {
            background: radial-gradient(circle, var(--sutala-loka-color) 0%, rgba(49,46,129,0.7) 100%);
            color: #fff;
            animation-delay: 1.3s;
        }

        .talatala-loka {
            background: radial-gradient(circle, var(--talatala-loka-color) 0%, rgba(30,27,75,0.7) 100%);
            color: #fff;
            animation-delay: 1.4s;
        }

        .mahatala-loka {
            background: radial-gradient(circle, var(--mahatala-loka-color) 0%, rgba(15,23,42,0.7) 100%);
            color: #fff;
            animation-delay: 1.5s;
        }

        .rasatala-loka {
            background: radial-gradient(circle, var(--rasatala-loka-color) 0%, rgba(12,10,36,0.7) 100%);
            color: #fff;
            animation-delay: 1.6s;
        }

        .patala-loka {
            background: radial-gradient(circle, var(--patala-loka-color) 0%, rgba(3,7,18,0.7) 100%);
            color: #fff;
            animation-delay: 1.7s;
        }

        .naraka {
            background: radial-gradient(circle, var(--naraka-color) 0%, rgba(127,29,29,0.7) 100%);
            color: #fff;
            animation-delay: 1.8s;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 40px rgba(127, 29, 29, 0.4);
        }

        .naraka::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: url('https://images.unsplash.com/photo-1588421357574-87938a86fa28?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');
            background-size: cover;
            opacity: 0.2;
            mix-blend-mode: overlay;
        }

        .naraka::after {
            content: '';
            position: absolute;
            width: 150%;
            height: 150%;
            top: -25%;
            left: -25%;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255,0,0,0.2) 0%, transparent 70%);
            animation: fireGlow 3s infinite ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        @keyframes earthGlow {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes fireGlow {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        .title {
            text-align: center;
            margin-bottom: 20px;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(to right, #f9fafb, #60a5fa);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            animation: titleGlow 3s infinite;
            position: relative;
            z-index: 10;
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.8;
            max-width: 800px;
            line-height: 1.6;
        }

        @keyframes titleGlow {
            0%, 100% { filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5)); }
            50% { filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.8)); }
        }

        .controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 12px 20px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(5px);
        }

        .control-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }

        .star {
            position: absolute;
            background-color: white;
            border-radius: 50%;
            animation: twinkle var(--duration) infinite ease-in-out;
            opacity: var(--opacity);
        }

        @keyframes twinkle {
            0%, 100% { opacity: var(--opacity); transform: scale(1); }
            50% { opacity: var(--opacity-half); transform: scale(0.5); }
        }

        .navigation {
            position: fixed;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 1000;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-dot.active {
            background-color: white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
        }

        .nav-dot:hover {
            transform: scale(1.2);
        }

        .nav-tooltip {
            position: absolute;
            left: 25px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        .nav-dot:hover .nav-tooltip {
            opacity: 1;
        }

        .presentation-mode {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .mode-toggle {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(5px);
        }

        .mode-toggle:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .cosmic-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 0;
        }
    </style>
</head>
<body class="bg-black text-white">
    <div class="cosmos-container">
        <h1 class="title">The Cosmic Hierarchy of Lokas</h1>
        <p class="subtitle">Explore the 14 cosmic planes of existence from the ancient Vedic cosmology</p>

        <div class="universe" id="universe">
            <!-- Starry background -->
            <div class="stars" id="stars"></div>

            <!-- Cosmic particles -->
            <div class="cosmic-particles" id="particles"></div>

            <!-- Param Dham and other higher realms -->
            <div class="loka param-dham" id="paramDham">
                <div class="loka-content">
                    <div class="loka-name">Param Dham</div>
                    <div>(Nirakari)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Param Dham (Nirakari)</div>
                    <div class="loka-info-description">
                        The highest realm beyond all material existence, the ultimate abode of the formless Supreme Being. This is the final destination for liberated souls who have transcended all material attachments.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 80px;">
                <div class="distance-label">Transcendental Distance</div>
            </div>

            <div class="loka shiva-puri" id="shivaPuri">
                <div class="loka-content">
                    <div class="loka-name">Shiva Puri</div>
                    <div>(Akari)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Shiva Puri (Akari)</div>
                    <div class="loka-info-description">
                        The abode of Lord Shiva and Shakti, representing the cosmic consciousness and divine energy. This realm embodies the principles of creation, preservation, and dissolution.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 80px;">
                <div class="distance-label">Divine Separation</div>
            </div>

            <div class="loka bishnu-puri" id="bishnuPuri">
                <div class="loka-content">
                    <div class="loka-name">Bishnu Puri</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Bishnu Puri</div>
                    <div class="loka-info-description">
                        The divine realm of Lord Vishnu, the preserver of the universe. This is where the cosmic order is maintained and from where divine incarnations descend to Earth.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 80px;">
                <div class="distance-label">Celestial Boundary</div>
            </div>

            <!-- Upper Lokas -->
            <div class="loka satya-loka" id="satyaLoka">
                <div class="loka-content">
                    <div class="loka-name">Satya Loka</div>
                    <div>(Brahma Loka)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Satya Loka (Brahma Loka)</div>
                    <div class="loka-info-description">
                        The Realm of Truth, the abode of Lord Brahma and the highest of the material realms. Those who reach here are liberated from the cycle of rebirth. This is where the universe is created.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Immeasurable Distance</div>
            </div>

            <div class="loka tapa-loka" id="tapaLoka">
                <div class="loka-content">
                    <div class="loka-name">Tapa Loka</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Tapa Loka</div>
                    <div class="loka-info-description">
                        The Realm of Austerity, inhabited by ascetics and tapaswis who perform intense spiritual practices. This realm vibrates with the energy of spiritual discipline and self-control.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Vast Cosmic Expanse</div>
            </div>

            <div class="loka jana-loka" id="janaLoka">
                <div class="loka-content">
                    <div class="loka-name">Jana Loka</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Jana Loka</div>
                    <div class="loka-info-description">
                        The Realm of Enlightened Beings, inhabited by the sons of Brahma and other spiritually evolved souls. This is where divine knowledge is preserved and transmitted.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Celestial Distance</div>
            </div>

            <div class="loka mahar-loka" id="maharLoka">
                <div class="loka-content">
                    <div class="loka-name">Mahar Loka</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Mahar Loka</div>
                    <div class="loka-info-description">
                        The Realm of Great Sages like the Saptarishis (Seven Sages), who survive even during the dissolution of the lower worlds. This realm is a repository of ancient wisdom.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Ethereal Expanse</div>
            </div>

            <div class="loka svar-loka" id="svarLoka">
                <div class="loka-content">
                    <div class="loka-name">Svar Loka</div>
                    <div>(Swarga)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Svar Loka (Swarga)</div>
                    <div class="loka-info-description">
                        Heaven, the realm of Indra and the devas (celestial beings). Those who perform good deeds enjoy the fruits of their karma here before returning to the cycle of rebirth.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Heavenly Boundary</div>
            </div>

            <div class="loka bhuvar-loka" id="bhuvarLoka">
                <div class="loka-content">
                    <div class="loka-name">Bhuvar Loka</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Bhuvar Loka</div>
                    <div class="loka-info-description">
                        The Realm between Earth and Heaven, inhabited by semi-divine beings, spirits, and ancestors. This is the astral plane where subtle beings reside.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 100px;">
                <div class="distance-label">Atmospheric Region</div>
            </div>

            <!-- Earth -->
            <div class="loka bhu-loka" id="bhuLoka">
                <div class="loka-content">
                    <div class="loka-name">Bhu Loka</div>
                    <div>(Earth)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Bhu Loka (Earth)</div>
                    <div class="loka-info-description">
                        The realm of humans and the material world, where karma is created through actions and spiritual evolution is possible. This is the plane where souls can work toward liberation.
                    </div>
                </div>
            </div>

            <!-- Lower Lokas with 10,000 Yojan distances -->
            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka atala-loka" id="atalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Atala Loka</div>
                    <div class="loka-distance">10,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Atala Loka</div>
                    <div class="loka-info-description">
                        The first of the lower realms, ruled by the demonic king Bala who created magical gemstones. Located 10,000 Yojans below Earth, this realm is filled with sensual pleasures.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka vitala-loka" id="vitalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Vitala Loka</div>
                    <div class="loka-distance">20,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Vitala Loka</div>
                    <div class="loka-info-description">
                        A realm with underground palaces and magical properties, where Lord Shiva's partial incarnation Hatakeswara presides. Located 20,000 Yojans below Earth's surface.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka sutala-loka" id="sutalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Sutala Loka</div>
                    <div class="loka-distance">30,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Sutala Loka</div>
                    <div class="loka-info-description">
                        The realm ruled by the virtuous demon king Bali, who was blessed by Lord Vishnu. Located 30,000 Yojans below Earth, this realm is more prosperous than the heavenly planets.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka talatala-loka" id="talatalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Talatala Loka</div>
                    <div class="loka-distance">40,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Talatala Loka</div>
                    <div class="loka-info-description">
                        The realm of the demon architect Maya, known for his expertise in magic and illusion. Located 40,000 Yojans below Earth, this realm is filled with technological marvels.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka mahatala-loka" id="mahatalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Mahatala Loka</div>
                    <div class="loka-distance">50,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Mahatala Loka</div>
                    <div class="loka-info-description">
                        The realm of serpent-like beings called Nagas, who possess many hoods. Located 50,000 Yojans below Earth, this realm is filled with powerful serpentine beings.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka rasatala-loka" id="rasatalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Rasatala Loka</div>
                    <div class="loka-distance">60,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Rasatala Loka</div>
                    <div class="loka-info-description">
                        The realm of the Danavas (demons) who are enemies of the devas and constantly wage war against them. Located 60,000 Yojans below Earth, this realm is filled with darkness.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 120px;">
                <div class="distance-label">10,000 Yojans (80,000 miles)</div>
            </div>

            <div class="loka patala-loka" id="patalaLoka">
                <div class="loka-content">
                    <div class="loka-name">Patala Loka</div>
                    <div class="loka-distance">70,000 Yojans below Earth</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Patala Loka</div>
                    <div class="loka-info-description">
                        The lowest of the netherworlds, inhabited by Nagas led by Vasuki and other serpent beings. Located 70,000 Yojans below Earth, this is the last of the lower realms.
                    </div>
                </div>
            </div>

            <div class="distance-indicator" style="height: 150px;">
                <div class="distance-label">Vast Infernal Depths</div>
            </div>

            <!-- Naraka (Hell) -->
            <div class="loka naraka" id="naraka">
                <div class="loka-content">
                    <div class="loka-name">Naraka</div>
                    <div>(55 Crore Hells)</div>
                </div>
                <div class="loka-info">
                    <div class="loka-info-title">Naraka (55 Crore Hells)</div>
                    <div class="loka-info-description">
                        The realms of punishment where souls experience the consequences of their negative karma before rebirth. These 55 crore (550 million) hells exist below Patala Loka in the darkest regions of the cosmos.
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="control-btn" id="autoScrollBtn">Auto Scroll</button>
            <button class="control-btn" id="resetBtn">Reset</button>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Create stars in the background
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 200; i++) {
                const star = document.createElement('div');
                star.classList.add('star');
                star.style.width = `${Math.random() * 3}px`;
                star.style.height = star.style.width;
                star.style.left = `${Math.random() * 100}%`;
                star.style.top = `${Math.random() * 100}%`;
                star.style.setProperty('--duration', `${Math.random() * 3 + 1}s`);
                star.style.setProperty('--opacity', `${Math.random() * 0.7 + 0.3}`);
                star.style.setProperty('--opacity-half', `${Math.random() * 0.3}`);
                starsContainer.appendChild(star);
            }

            // Create cosmic particles
            const particlesContainer = document.getElementById('particles');
            const particleColors = [
                'rgba(147, 197, 253, 0.6)', // Blue
                'rgba(167, 139, 250, 0.6)', // Purple
                'rgba(251, 191, 36, 0.6)',  // Yellow
                'rgba(248, 113, 113, 0.6)', // Red
                'rgba(52, 211, 153, 0.6)'   // Green
            ];

            for (let i = 0; i < 50; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.width = `${Math.random() * 6 + 2}px`;
                particle.style.height = particle.style.width;
                particle.style.borderRadius = '50%';
                particle.style.backgroundColor = particleColors[Math.floor(Math.random() * particleColors.length)];
                particle.style.boxShadow = `0 0 ${Math.random() * 10 + 5}px ${particle.style.backgroundColor}`;
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.top = `${Math.random() * 100}%`;

                // Create floating animation
                const duration = Math.random() * 20 + 10;
                const xMovement = Math.random() * 30 - 15;
                const yMovement = Math.random() * 30 - 15;

                particle.style.animation = `
                    floatParticle ${duration}s infinite ease-in-out,
                    pulseParticle ${Math.random() * 3 + 2}s infinite ease-in-out
                `;

                // Create keyframes for this specific particle
                const keyframes = `
                @keyframes floatParticle {
                    0%, 100% { transform: translate(0, 0); }
                    50% { transform: translate(${xMovement}px, ${yMovement}px); }
                }
                @keyframes pulseParticle {
                    0%, 100% { opacity: ${Math.random() * 0.5 + 0.3}; }
                    50% { opacity: ${Math.random() * 0.3 + 0.7}; }
                }`;

                const style = document.createElement('style');
                style.textContent = keyframes;
                document.head.appendChild(style);

                particlesContainer.appendChild(particle);
            }

            // Setup navigation dots
            const universe = document.getElementById('universe');
            const lokas = document.querySelectorAll('.loka');
            const navigation = document.createElement('div');
            navigation.classList.add('navigation');
            document.querySelector('.cosmos-container').appendChild(navigation);

            lokas.forEach((loka, index) => {
                const navDot = document.createElement('div');
                navDot.classList.add('nav-dot');
                const tooltip = document.createElement('div');
                tooltip.classList.add('nav-tooltip');
                tooltip.textContent = loka.querySelector('.loka-name').textContent;
                navDot.appendChild(tooltip);

                navDot.addEventListener('click', () => {
                    loka.scrollIntoView({ behavior: 'smooth', block: 'center' });

                    // Toggle active state for info panel
                    lokas.forEach(l => l.classList.remove('active'));
                    loka.classList.add('active');
                });

                navigation.appendChild(navDot);
            });

            // Auto scroll functionality
            const autoScrollBtn = document.getElementById('autoScrollBtn');
            const resetBtn = document.getElementById('resetBtn');
            let isAutoScrolling = false;
            let autoScrollInterval;

            function autoScroll() {
                if (isAutoScrolling) {
                    clearInterval(autoScrollInterval);
                    autoScrollBtn.textContent = 'Auto Scroll';
                    autoScrollBtn.innerHTML = '<i class="fas fa-play"></i> Auto Tour';
                    isAutoScrolling = false;
                } else {
                    let currentLoka = 0;

                    function scrollToNextLoka() {
                        if (currentLoka < lokas.length) {
                            lokas[currentLoka].scrollIntoView({ behavior: 'smooth', block: 'center' });

                            // Update active states
                            lokas.forEach(l => l.classList.remove('active'));
                            lokas[currentLoka].classList.add('active');

                            // Update navigation dots
                            const navDots = document.querySelectorAll('.nav-dot');
                            navDots.forEach((dot, i) => {
                                dot.classList.toggle('active', i === currentLoka);
                            });

                            currentLoka++;
                            setTimeout(scrollToNextLoka, 3000); // Wait 3 seconds before moving to next loka
                        } else {
                            // Reset when reached the end
                            currentLoka = 0;
                            setTimeout(scrollToNextLoka, 1000);
                        }
                    }

                    scrollToNextLoka();
                    autoScrollBtn.innerHTML = '<i class="fas fa-pause"></i> Stop Tour';
                    isAutoScrolling = true;
                }
            }

            // Reset function
            function reset() {
                if (isAutoScrolling) {
                    clearInterval(autoScrollInterval);
                    autoScrollBtn.innerHTML = '<i class="fas fa-play"></i> Auto Tour';
                    isAutoScrolling = false;
                }
                window.scrollTo({ top: 0, behavior: 'smooth' });
                lokas.forEach(l => l.classList.remove('active'));
            }

            // Make lokas interactive
            lokas.forEach(loka => {
                loka.addEventListener('click', function() {
                    // Toggle active state
                    lokas.forEach(l => l.classList.remove('active'));
                    this.classList.add('active');

                    if (isAutoScrolling) {
                        clearInterval(autoScrollInterval);
                        autoScrollBtn.innerHTML = '<i class="fas fa-play"></i> Auto Tour';
                        isAutoScrolling = false;
                    }
                });
            });

            // Event listeners
            autoScrollBtn.innerHTML = '<i class="fas fa-play"></i> Auto Tour';
            autoScrollBtn.addEventListener('click', autoScroll);
            resetBtn.innerHTML = '<i class="fas fa-undo"></i> Reset';
            resetBtn.addEventListener('click', reset);

            // Add presentation mode toggle
            const presentationMode = document.createElement('div');
            presentationMode.classList.add('presentation-mode');
            const modeToggle = document.createElement('button');
            modeToggle.classList.add('mode-toggle');
            modeToggle.innerHTML = '<i class="fas fa-chalkboard"></i> Presentation Mode';
            modeToggle.addEventListener('click', () => {
                document.body.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            });
            presentationMode.appendChild(modeToggle);
            document.querySelector('.cosmos-container').appendChild(presentationMode);
        });
    </script>
</body>
</html>
