<?php

namespace App\Http\Controllers;

use App\Exports\ProductsExport;
use App\Models\Product;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class LazyTableController extends Controller
{
    use HasDataTable;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->tableModel = Product::class;
        $this->exportClass = ProductsExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'stock' => 'Stock',
            'status' => 'Status',
            'created_at' => 'Date',
            'image_url' => 'Image'
        ];
        $this->sortableColumns = ['id', 'name', 'category', 'price', 'stock', 'status', 'created_at'];
    }

    /**
     * Display a listing of the resource with lazy loading support.
     */
    public function index(Request $request)
    {
        // Add lazy_load parameter to request
        $request->merge(['lazy_load' => true]);

        return $this->handleDataTable($request, 'lazy-table.index');
    }
}
