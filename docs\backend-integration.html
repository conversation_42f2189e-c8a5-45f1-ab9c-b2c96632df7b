<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backend Integration - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="custom-logic.html">Custom Logic</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="backend-integration.html">Backend Integration</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Backend Integration</h1>
            <p class="lead">Learn how to integrate the Dynamic Table component with your custom backend logic and data sources.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The Dynamic Table component is designed to be flexible and can work with various backend implementations and data sources.
            </div>
            
            <div class="section-card">
                <h2>Expected Response Format</h2>
                <p>The Dynamic Table component expects a specific JSON response format for AJAX requests. Your backend logic must return data in this format for the component to work properly.</p>
                
                <h3>Standard Table Response Format</h3>
                <pre><code>{
    "table": "&lt;rendered HTML for table rows&gt;",
    "pagination": "&lt;rendered HTML for pagination&gt;",
    "success": true,
    "message": "Data loaded successfully",
    "total": 100,
    "per_page": 10,
    "current_page": 1,
    "last_page": 10,
    "sort": "name",
    "direction": "asc",
    "append": false
}</code></pre>
                
                <h3>Lazy Loading Table Response Format</h3>
                <pre><code>{
    "html": "&lt;rendered HTML for table rows&gt;",
    "hasMorePages": true,
    "nextPage": 2,
    "totalCount": 100,
    "currentCount": 10,
    "success": true,
    "message": "Loaded items 1 to 10 of 100",
    "append": true,
    "sort": "name",
    "direction": "asc"
}</code></pre>
                <p>If you're implementing a custom backend, you need to ensure that your API or controller returns data in these formats.</p>
            </div>
            
            <div class="section-card">
                <h2>Integration Approaches</h2>
                <p>There are several ways to integrate the Dynamic Table component with your backend:</p>
                
                <h3>1. Using the HasDataTable Trait (Recommended)</h3>
                <p>The easiest way is to use the provided <code>HasDataTable</code> trait and override specific methods to implement your custom logic:</p>
                <pre><code>class YourController extends Controller
{
    use HasDataTable;
    
    // Override this method to implement custom query logic
    protected function buildTableQuery(Request $request)
    {
        // Start with a base query
        $query = YourModel::query();
        
        // Apply your custom logic
        if ($request->has('custom_param')) {
            $query->where('custom_field', $request->input('custom_param'));
        }
        
        // Join with related tables if needed
        $query->leftJoin('related_table', 'your_model.related_id', '=', 'related_table.id')
              ->select('your_model.*', 'related_table.name as related_name');
        
        return $query;
    }
}</code></pre>
                <p>This approach allows you to customize the query while still leveraging the standard filtering, sorting, and pagination functionality provided by the trait.</p>
                
                <h3>2. Custom Data Source</h3>
                <p>If your data comes from a non-standard source (e.g., an external API), you can override the <code>getBaseQuery</code> method:</p>
                <pre><code>protected function getBaseQuery()
{
    // Example: Use a repository
    return app(YourRepository::class)->getQuery();
    
    // Example: Use an API data source
    $apiData = Http::get('https://api.example.com/data')->json();
    return collect($apiData);
}</code></pre>
                
                <h3>3. Completely Custom Implementation</h3>
                <p>If you need full control, you can implement your own controller without using the <code>HasDataTable</code> trait:</p>
                <pre><code>class CustomController extends Controller
{
    public function index(Request $request)
    {
        // Get data using any custom logic
        $data = $this->getDataFromAnywhere($request);
        
        // Format data for the table
        $items = $this->formatData($data);
        
        // Create a paginator (if needed)
        $paginator = new LengthAwarePaginator(
            $items,
            $this->getTotalCount(),
            $request->input('per_page', 10),
            $request->input('page', 1)
        );
        
        // Handle AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'table' => view('components.data-table.table-rows', ['items' => $paginator])->render(),
                'pagination' => view('components.data-table.pagination', ['items' => $paginator])->render(),
                'success' => true,
                'message' => 'Data loaded successfully',
                'total' => $this->getTotalCount(),
                'per_page' => $request->input('per_page', 10),
                'current_page' => $request->input('page', 1),
                'last_page' => ceil($this->getTotalCount() / $request->input('per_page', 10)),
                'sort' => $request->input('sort'),
                'direction' => $request->input('direction', 'asc'),
            ]);
        }
        
        // Return view for non-AJAX requests
        return view('your-view', [
            'items' => $paginator,
            'filterOptions' => $this->getFilterOptions(),
        ]);
    }
    
    // User can implement any method to get data from anywhere
    protected function getDataFromAnywhere(Request $request)
    {
        // Your custom logic here
    }
}</code></pre>
                <p>This approach gives you complete control over how data is fetched and processed, but requires you to manually handle all aspects of the table functionality.</p>
            </div>
            
            <div class="section-card">
                <h2>Working with External APIs</h2>
                <p>If your data comes from an external API, you can integrate it with the Dynamic Table component as follows:</p>
                
                <h3>1. Create a Controller</h3>
                <pre><code>class ApiTableController extends Controller
{
    use HasDataTable;
    
    protected function buildTableQuery(Request $request)
    {
        // We'll override the handleDataTable method instead
        return null;
    }
    
    public function handleDataTable(Request $request, $viewPath)
    {
        // Get parameters for the API request
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search');
        $sort = $request->input('sort');
        $direction = $request->input('direction', 'asc');
        
        // Make API request
        $response = Http::get('https://api.example.com/data', [
            'page' => $page,
            'limit' => $perPage,
            'search' => $search,
            'sort_by' => $sort,
            'sort_dir' => $direction,
        ]);
        
        // Parse API response
        $data = $response->json();
        
        // Create a collection from the API data
        $items = collect($data['items'] ?? []);
        
        // Create a paginator
        $paginator = new LengthAwarePaginator(
            $items,
            $data['total'] ?? 0,
            $perPage,
            $page,
            ['path' => $request->url()]
        );
        
        // Handle AJAX requests
        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'table' => view('components.data-table.table-rows', ['items' => $paginator])->render(),
                'pagination' => view('components.data-table.pagination', ['items' => $paginator])->render(),
                'success' => true,
                'message' => 'Data loaded successfully',
                'total' => $data['total'] ?? 0,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => ceil(($data['total'] ?? 0) / $perPage),
                'sort' => $sort,
                'direction' => $direction,
            ]);
        }
        
        // Return view for non-AJAX requests
        return view($viewPath, [
            'items' => $paginator,
            'filterOptions' => $this->getFilterOptions(),
        ]);
    }
    
    protected function getFilterOptions()
    {
        // Get filter options from API or hardcode them
        $response = Http::get('https://api.example.com/filter-options');
        $data = $response->json();
        
        return [
            'categories' => $data['categories'] ?? [],
            'statuses' => $data['statuses'] ?? [],
        ];
    }
}</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Handling Different Data Formats</h2>
                <p>If your data is in a different format than what the component expects, you can transform it before passing it to the view:</p>
                
                <h3>1. Transform API Data</h3>
                <pre><code>protected function transformApiData($apiData)
{
    return collect($apiData)->map(function ($item) {
        return [
            'id' => $item['id'] ?? null,
            'name' => $item['title'] ?? null, // Map 'title' to 'name'
            'category' => $item['category']['name'] ?? null, // Extract nested data
            'price' => $item['pricing']['amount'] ?? null, // Extract nested data
            'status' => $this->mapStatus($item['status_code'] ?? null), // Transform status code
            'created_at' => Carbon::parse($item['created_at']), // Parse date
        ];
    });
}

protected function mapStatus($statusCode)
{
    $statusMap = [
        1 => 'active',
        2 => 'inactive',
        3 => 'pending',
        4 => 'out_of_stock',
    ];
    
    return $statusMap[$statusCode] ?? 'unknown';
}</code></pre>
                
                <h3>2. Handle Nested or Complex Data</h3>
                <p>If your data contains nested or complex structures, you can use custom column renderers to display it properly:</p>
                <pre><code>public function __construct()
{
    // Other properties...
    
    $this->columnRenderers = [
        'complex_field' => function($item) {
            if (isset($item->complex_field) && is_array($item->complex_field)) {
                $html = '<ul>';
                foreach ($item->complex_field as $key => $value) {
                    $html .= '<li>' . e($key) . ': ' . e($value) . '</li>';
                }
                $html .= '</ul>';
                return $html;
            }
            return 'N/A';
        },
    ];
}</code></pre>
            </div>
            
            <div class="mt-4">
                <p>These examples demonstrate how to integrate the Dynamic Table component with various backend implementations and data sources. You can adapt these approaches to fit your specific requirements and data formats.</p>
                <a href="examples.html" class="btn btn-primary">See More Examples</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
