<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sorting - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Sorting</h1>
            <p class="lead">Learn how to use the sorting feature of the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> The sorting feature allows users to sort the table data by clicking on column headers. All sorting operations are performed without page refresh using AJAX.
            </div>
            
            <div class="section-card">
                <h2>Configuring Sortable Columns</h2>
                <p>You can configure which columns are sortable in your controller by setting the <code>$sortableColumns</code> property:</p>
                <pre><code>public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class;
    $this->tableColumns = [
        'id' => 'ID',
        'name' => 'Name',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status',
        'created_at' => 'Date',
    ];
    
    // Configure sortable columns
    $this->sortableColumns = ['id', 'name', 'category', 'price', 'status', 'created_at'];
    
    // Filter fields...
}</code></pre>
                <p>The <code>$sortableColumns</code> array contains the column keys that should be sortable. Only columns listed in this array will have sort links in the table header.</p>
            </div>
            
            <div class="section-card">
                <h2>Specifying Sortable Columns in the View</h2>
                <p>You also need to specify the sortable columns in the view when using the table component:</p>
                <pre><code>&lt;x-data-table
    :items="$items"
    :columns="[
        'id' => 'ID',
        'name' => 'Name',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status',
        'created_at' => 'Date',
    ]"
    :sortable="['id', 'name', 'category', 'price', 'status', 'created_at']"
    :filter-options="$filterOptions"
    title="Your Table Title"
    route-name="your-table.index"
/&gt;</code></pre>
                <p>The <code>:sortable</code> prop should match the <code>$sortableColumns</code> property in your controller.</p>
            </div>
            
            <div class="section-card">
                <h2>How Sorting Works</h2>
                <p>The sorting feature works as follows:</p>
                <ol>
                    <li>When a user clicks on a sortable column header, the table data is sorted by that column</li>
                    <li>The first click sorts the column in ascending order</li>
                    <li>Clicking the same column again toggles between ascending and descending order</li>
                    <li>The current sort column and direction are indicated by an up or down arrow icon</li>
                    <li>All sorting operations are performed without page refresh using AJAX</li>
                </ol>
                <p>The sorting is implemented using the following components:</p>
                <ul>
                    <li>Sort links in the table header with appropriate URLs</li>
                    <li>JavaScript event handlers to intercept clicks on sort links</li>
                    <li>AJAX requests to fetch sorted data</li>
                    <li>Dynamic updating of the table content and sort indicators</li>
                </ul>
            </div>
            
            <div class="section-card">
                <h2>Sort Links</h2>
                <p>The sort links in the table header are generated automatically based on the <code>:sortable</code> prop. Each sort link includes:</p>
                <ul>
                    <li>The column name as the link text</li>
                    <li>A sort icon indicating the current sort direction (if the column is currently sorted)</li>
                    <li>A URL with the appropriate sort parameters</li>
                    <li>A data attribute indicating the column key</li>
                </ul>
                <p>Here's an example of the HTML generated for a sort link:</p>
                <pre><code>&lt;a href="/your-table?sort=name&direction=asc" class="sort-link" data-column="name"&gt;
    Name
    &lt;i class="fas fa-sort"&gt;&lt;/i&gt;
&lt;/a&gt;</code></pre>
                <p>When the column is currently sorted, the icon changes to indicate the sort direction:</p>
                <pre><code>&lt;!-- Ascending sort --&gt;
&lt;a href="/your-table?sort=name&direction=desc" class="sort-link" data-column="name"&gt;
    Name
    &lt;i class="fas fa-sort-up"&gt;&lt;/i&gt;
&lt;/a&gt;

&lt;!-- Descending sort --&gt;
&lt;a href="/your-table?sort=name&direction=asc" class="sort-link" data-column="name"&gt;
    Name
    &lt;i class="fas fa-sort-down"&gt;&lt;/i&gt;
&lt;/a&gt;</code></pre>
            </div>
            
            <div class="section-card">
                <h2>Combining Sorting with Filtering</h2>
                <p>The sorting feature can be combined with filtering. When a user applies filters and then sorts the table, the filters are preserved in the sort links.</p>
                <p>For example, if a user filters the table to show only active products and then sorts by name, the sort link will include both the sort parameters and the filter parameters:</p>
                <pre><code>&lt;a href="/your-table?status=active&sort=name&direction=asc" class="sort-link" data-column="name"&gt;
    Name
    &lt;i class="fas fa-sort"&gt;&lt;/i&gt;
&lt;/a&gt;</code></pre>
                <p>This ensures that the filters are preserved when sorting the table.</p>
            </div>
            
            <div class="section-card">
                <h2>Default Sorting</h2>
                <p>You can specify a default sort column and direction in your controller:</p>
                <pre><code>public function index(Request $request)
{
    // Set default sort parameters if not provided
    if (!$request->has('sort')) {
        $request->merge(['sort' => 'created_at', 'direction' => 'desc']);
    }
    
    return $this->handleDataTable($request, 'your-view-path');
}</code></pre>
                <p>This will sort the table by the specified column and direction when the page is first loaded.</p>
            </div>
            
            <div class="mt-4">
                <p>Next, learn how to use the pagination feature:</p>
                <a href="pagination.html" class="btn btn-primary">Pagination Guide</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
