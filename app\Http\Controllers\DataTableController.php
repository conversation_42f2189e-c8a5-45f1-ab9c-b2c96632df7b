<?php

namespace App\Http\Controllers;

use App\Exports\ProductsExport;
use App\Models\Product;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class DataTableController extends Controller
{
    use HasDataTable;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->tableModel = Product::class;
        $this->exportClass = ProductsExport::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            'category' => 'Category',
            'price' => 'Price',
            'stock' => 'Stock',
            'status' => 'Status',
            'created_at' => 'Date',
            'image_url' => 'Image'
        ];
        $this->sortableColumns = ['id', 'name', 'category', 'price', 'stock', 'status', 'created_at'];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        return $this->handleDataTable($request, 'data-table.index');
    }
}
