<?php

use App\Http\Controllers\DataTableController;
use App\Http\Controllers\LazyTableController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\RoomController;
use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome', [
        'canLogin' => Route::has('login'),
        'canRegister' => Route::has('register'),
        'laravelVersion' => Application::VERSION,
        'phpVersion' => PHP_VERSION,
    ]);
});

Route::get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/room/{room:slug}', [RoomController::class, 'show'])->name('room.show');
    Route::get('/room/{room:slug}/messages', [MessageController::class, 'index'])->name('message.index');
    Route::post('/room/{room:slug}/messages', [MessageController::class, 'store'])->name('message.store');
});

// Data Table Routes
Route::get('/data-table', [DataTableController::class, 'index'])->name('data-table.index');

// Lazy Loading Table Routes
Route::get('/lazy-table', [LazyTableController::class, 'index'])->name('lazy-table.index');
Route::get('/products', [ProductController::class, 'index'])->name('products.index');

require __DIR__ . '/auth.php';

