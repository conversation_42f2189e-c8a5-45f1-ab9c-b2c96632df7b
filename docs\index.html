<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        .feature-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 2rem;
            color: #4f46e5;
            margin-bottom: 1rem;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .btn-primary {
            background-color: #4f46e5;
            border-color: #4f46e5;
        }
        .btn-primary:hover {
            background-color: #4338ca;
            border-color: #4338ca;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link active" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="custom-logic.html">Custom Logic</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="backend-integration.html">Backend Integration</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Dynamic Table Component for Laravel</h1>
            <p class="lead">A powerful, reusable table component for Laravel applications with refresh-free operations, filtering, sorting, pagination, lazy loading, and export functionality.</p>

            <div class="alert alert-primary" role="alert">
                <i class="fas fa-info-circle me-2"></i> This component is designed to be easily integrated into any Laravel project with minimal configuration.
            </div>

            <div class="row mt-5">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <h3>Refresh-Free Operations</h3>
                        <p>All actions (filtering, sorting, pagination) are performed without page refresh using AJAX.</p>
                        <a href="basic-usage.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-filter"></i>
                        </div>
                        <h3>Advanced Filtering</h3>
                        <p>Comprehensive filtering options with a slide-in sidebar and immediate updates.</p>
                        <a href="filtering.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-sort"></i>
                        </div>
                        <h3>Column Sorting</h3>
                        <p>Click column headers to sort data in ascending or descending order instantly.</p>
                        <a href="sorting.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-pager"></i>
                        </div>
                        <h3>Pagination</h3>
                        <p>Standard pagination with configurable items per page and AJAX-based navigation.</p>
                        <a href="pagination.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-spinner"></i>
                        </div>
                        <h3>Lazy Loading</h3>
                        <p>Automatically load more data when scrolling to the bottom of the table.</p>
                        <a href="lazy-loading.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-file-export"></i>
                        </div>
                        <h3>Export Functionality</h3>
                        <p>Export data in Excel, CSV, and PDF formats with a single click.</p>
                        <a href="exporting.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3>Custom Logic</h3>
                        <p>Adapt the component to your specific data formats and implement custom business logic.</p>
                        <a href="custom-logic.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <h3>Backend Integration</h3>
                        <p>Integrate with any backend data source including APIs, databases, or custom services.</p>
                        <a href="backend-integration.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-paint-brush"></i>
                        </div>
                        <h3>Customization</h3>
                        <p>Customize the appearance and behavior of the table to match your application's design.</p>
                        <a href="customization.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
            </div>

            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3>Examples</h3>
                        <p>Explore real-world examples of the Dynamic Table component in action.</p>
                        <a href="examples.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <h3>Troubleshooting</h3>
                        <p>Find solutions to common issues and learn how to debug problems.</p>
                        <a href="troubleshooting.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-card text-center">
                        <div class="feature-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3>Documentation</h3>
                        <p>Comprehensive documentation to help you get the most out of the component.</p>
                        <a href="index.html" class="btn btn-primary">Learn More</a>
                    </div>
                </div>
            </div>

            <div class="mt-5">
                <h2>Getting Started</h2>
                <p>To get started with the Dynamic Table component, follow these steps:</p>
                <ol>
                    <li>Install the component in your Laravel project</li>
                    <li>Include the required assets</li>
                    <li>Create a controller with the HasDataTable trait</li>
                    <li>Create a view with the table component</li>
                    <li>Register the components in your service provider</li>
                </ol>
                <p>For detailed installation instructions, see the <a href="installation.html">Installation Guide</a>.</p>
            </div>

            <div class="mt-5">
                <h2>Basic Example</h2>
                <pre><code>&lt;x-data-table
    :items="$items"
    :filter-options="$filterOptions"
    :columns="[
        'id' => 'ID',
        'name' => 'Name',
        'category' => 'Category',
        'price' => 'Price',
        'status' => 'Status'
    ]"
    :sortable="['id', 'name', 'category', 'price', 'status']"
    route-name="products.index"
    title="Products"
/&gt;</code></pre>
                <p>For more examples, see the <a href="examples.html">Examples</a> page.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
