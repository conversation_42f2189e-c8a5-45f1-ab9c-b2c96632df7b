@props([
    'items',
    'columns',
    'sortable' => [],
    'searchable' => true,
    'filterable' => true,
    'pagination' => true,
    'lazyLoad' => false,
    'exportable' => true,
    'exportFormats' => ['excel', 'csv', 'pdf'],
    'filterOptions' => [],
    'title' => 'Data Table',
    'tableClass' => '',
    'routeName' => 'data-table.index',
    'tableId' => 'data-table-' . \Illuminate\Support\Str::random(8),
])

<div class="dynamic-table-container" id="{{ $tableId }}">
    <!-- Filter Sidebar -->
    @if($filterable)
    <div class="filter-sidebar" id="filterSidebar-{{ $tableId }}">
        <div class="filter-header">
            <h3>Filters</h3>
            <button type="button" class="close-filters" id="closeFilters-{{ $tableId }}">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <form id="filterForm-{{ $tableId }}" action="{{ route($routeName) }}" method="GET">
            @if(request()->has('sort'))
                <input type="hidden" name="sort" value="{{ request()->input('sort') }}">
            @endif

            @if(request()->has('direction'))
                <input type="hidden" name="direction" value="{{ request()->input('direction') }}">
            @endif

            <div class="filter-section">
                <h4>Search</h4>
                <div class="filter-input">
                    <input type="text" id="searchInput" name="search" placeholder="Search..." value="{{ request()->input('search') }}">
                </div>
            </div>

            @if(isset($filterOptions['categories']))
            <div class="filter-section">
                <h4>Category</h4>
                <div class="filter-input">
                    <select name="category" id="categoryFilter-{{ $tableId }}">
                        <option value="">All Categories</option>
                        @foreach($filterOptions['categories'] as $category)
                            <option value="{{ $category }}" {{ request()->input('category') == $category ? 'selected' : '' }}>
                                {{ $category }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            @endif

            @if(isset($filterOptions['statuses']))
            <div class="filter-section">
                <h4>Status</h4>
                <div class="filter-input">
                    <select name="status" id="statusFilter-{{ $tableId }}">
                        <option value="">All Statuses</option>
                        @foreach($filterOptions['statuses'] as $status)
                            <option value="{{ $status }}" {{ request()->input('status') == $status ? 'selected' : '' }}>
                                {{ ucfirst(str_replace('_', ' ', $status)) }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>
            @endif

            <div class="filter-section">
                <h4>Price Range</h4>
                <div class="filter-input">
                    <label>Min</label>
                    <input type="number" id="priceMin" name="price_min" value="{{ request()->input('price_min') }}" min="0" step="0.01">
                </div>
                <div class="filter-input">
                    <label>Max</label>
                    <input type="number" id="priceMax" name="price_max" value="{{ request()->input('price_max') }}" min="0" step="0.01">
                </div>
            </div>

            <div class="filter-section">
                <h4>Date Range</h4>
                <div class="filter-input">
                    <label>From</label>
                    <input type="date" id="dateFrom" name="date_from" value="{{ request()->input('date_from') }}">
                </div>
                <div class="filter-input">
                    <label>To</label>
                    <input type="date" id="dateTo" name="date_to" value="{{ request()->input('date_to') }}">
                </div>
            </div>

            <div class="filter-actions">
                <button type="submit" class="btn btn-apply" id="applyFilters-{{ $tableId }}">Apply Filters</button>
                <button type="button" class="btn btn-reset" id="resetFilters-{{ $tableId }}">Reset</button>
            </div>
        </form>
    </div>
    @endif

    <!-- Main Content -->
    <div class="table-content">
        <div class="table-header">
            <h2>{{ $title }}</h2>
            <div class="table-actions">
                @if($filterable)
                <button class="btn btn-filter" id="showFilters-{{ $tableId }}">
                    <i class="fas fa-filter"></i> Filters
                </button>
                @endif

                @if($searchable)
                <div class="search-container">
                    <input type="text" id="quickSearch-{{ $tableId }}" placeholder="Quick search..." class="quick-search">
                    <button type="button" id="quickSearchBtn-{{ $tableId }}">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                @endif

                @if($exportable)
                <div class="export-buttons">
                    <div class="dropdown">
                        <button class="btn btn-export" id="exportDropdown-{{ $tableId }}">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <div class="dropdown-menu" id="exportMenu-{{ $tableId }}">
                            @if(in_array('excel', $exportFormats))
                            <a href="{{ route($routeName, array_merge(request()->all(), ['export' => 'excel'])) }}" class="dropdown-item" target="_blank">
                                <i class="fas fa-file-excel"></i> Excel
                            </a>
                            @endif

                            @if(in_array('csv', $exportFormats))
                            <a href="{{ route($routeName, array_merge(request()->all(), ['export' => 'csv'])) }}" class="dropdown-item" target="_blank">
                                <i class="fas fa-file-csv"></i> CSV
                            </a>
                            @endif

                            @if(in_array('pdf', $exportFormats))
                            <a href="{{ route($routeName, array_merge(request()->all(), ['export' => 'pdf'])) }}" class="dropdown-item" target="_blank">
                                <i class="fas fa-file-pdf"></i> PDF
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <div class="table-responsive">
            <table class="data-table {{ $tableClass }}" id="dataTable-{{ $tableId }}">
                <thead>
                    <tr>
                        @foreach($columns as $key => $column)
                            <th @if(in_array($key, $sortable)) class="sortable" @endif>
                                @if(in_array($key, $sortable))
                                    <a href="{{ route($routeName, array_merge(
                                        request()->except(['sort', 'direction']),
                                        [
                                            'sort' => $key,
                                            'direction' => request()->input('sort') === $key && request()->input('direction') === 'asc' ? 'desc' : 'asc'
                                        ]
                                    )) }}" class="sort-link" data-column="{{ $key }}">
                                        {{ $column }}
                                        <i class="fas fa-sort{{ request()->input('sort') === $key ? (request()->input('direction') === 'asc' ? '-up' : '-down') : '' }}"></i>
                                    </a>
                                @else
                                    {{ $column }}
                                @endif
                            </th>
                        @endforeach
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="tableBody-{{ $tableId }}">
                    @include('components.data-table.table-rows', ['items' => $items])
                </tbody>
            </table>
        </div>

        <div class="table-footer">
            @if($pagination)
            <div class="pagination-container" id="paginationContainer-{{ $tableId }}">
                @include('components.data-table.pagination', ['items' => $items])
            </div>
            <div class="pagination-options">
                <label>
                    Show
                    <select id="itemsPerPage-{{ $tableId }}">
                        <option value="10" {{ request()->input('per_page', 10) == 10 ? 'selected' : '' }}>10</option>
                        <option value="25" {{ request()->input('per_page', 10) == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request()->input('per_page', 10) == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request()->input('per_page', 10) == 100 ? 'selected' : '' }}>100</option>
                    </select>
                    entries
                </label>
            </div>
            @endif
        </div>
    </div>
</div>
