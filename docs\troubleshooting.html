<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Troubleshooting - Dynamic Table Component Documentation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            padding-top: 56px;
        }
        .sidebar {
            position: fixed;
            top: 56px;
            left: 0;
            height: calc(100vh - 56px);
            width: 250px;
            background-color: #f8f9fa;
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #dee2e6;
        }
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        .nav-link {
            color: #495057;
            border-radius: 0.25rem;
            padding: 0.5rem 1rem;
        }
        .nav-link:hover {
            background-color: #e9ecef;
        }
        .nav-link.active {
            background-color: #4f46e5;
            color: white;
        }
        pre {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.5rem;
            overflow-x: auto;
        }
        code {
            color: #e83e8c;
        }
        .section-card {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .feature-image {
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            max-width: 100%;
            height: auto;
            margin: 1rem 0;
        }
        .problem {
            font-weight: 600;
            color: #dc3545;
        }
        .solution {
            font-weight: 600;
            color: #198754;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.html">Dynamic Table</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.html">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="installation.html">Installation</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="examples.html">Examples</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="sidebar">
        <h5>Documentation</h5>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link" href="index.html">Overview</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="installation.html">Installation</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="basic-usage.html">Basic Usage</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="filtering.html">Filtering</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="sorting.html">Sorting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="pagination.html">Pagination</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="lazy-loading.html">Lazy Loading</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="exporting.html">Exporting</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="customization.html">Customization</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="examples.html">Examples</a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="troubleshooting.html">Troubleshooting</a>
            </li>
        </ul>
    </div>

    <div class="main-content">
        <div class="container">
            <h1 class="mb-4">Troubleshooting</h1>
            <p class="lead">Solutions to common issues with the Dynamic Table component.</p>
            
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> If you encounter an issue that is not covered here, please check the browser console for error messages or contact support.
            </div>
            
            <div class="section-card">
                <h2>Installation Issues</h2>
                
                <div class="mb-4">
                    <p class="problem">Problem: Composer package installation fails with dependency errors.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure your Laravel version is compatible with the required packages (Laravel 8.0+ recommended)</li>
                        <li>Try updating your composer dependencies: <code>composer update</code></li>
                        <li>If specific package conflicts occur, try installing them individually with specific versions</li>
                    </ol>
                    <pre><code>composer require maatwebsite/excel:^3.1
composer require barryvdh/laravel-dompdf:^2.0</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Component registration fails with "Class not found" error.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the component classes are in the correct namespace</li>
                        <li>Run <code>composer dump-autoload</code> to refresh the autoloader</li>
                        <li>Check that the component classes are properly imported in your <code>AppServiceProvider.php</code></li>
                    </ol>
                    <pre><code>// In AppServiceProvider.php
use App\View\Components\DataTable;
use App\View\Components\LazyTable;
use Illuminate\Support\Facades\Blade;

public function boot()
{
    // Register components
    Blade::component('data-table', DataTable::class);
    Blade::component('lazy-table', LazyTable::class);
}</code></pre>
                </div>
            </div>
            
            <div class="section-card">
                <h2>JavaScript Issues</h2>
                
                <div class="mb-4">
                    <p class="problem">Problem: AJAX requests fail with 419 (CSRF token mismatch) error.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the CSRF token meta tag is included in your layout</li>
                        <li>Check that jQuery is loaded before the dynamic-table.js script</li>
                        <li>Verify that the CSRF token is being properly sent with AJAX requests</li>
                    </ol>
                    <pre><code>&lt;!-- In your layout file --&gt;
&lt;meta name="csrf-token" content="{{ csrf_token() }}"&gt;

&lt;script&gt;
    // Set CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });
&lt;/script&gt;</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Table doesn't update when sorting or filtering.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Check the browser console for JavaScript errors</li>
                        <li>Verify that the table elements have the correct IDs</li>
                        <li>Make sure the AJAX response contains the expected data</li>
                        <li>Check that the event handlers are properly attached</li>
                    </ol>
                    <p>You can add debug logging to see what's happening:</p>
                    <pre><code>// In dynamic-table.js
function fetchTableData(url, closeSidebar = true) {
    console.log('Fetching data from:', url);
    
    // Rest of the function...
    
    success: function(data) {
        console.log('Data received:', data);
        
        // Rest of the success handler...
    },
    error: function(xhr, status, error) {
        console.error('Error fetching table data:', error);
        console.error('Response:', xhr.responseText);
        
        // Rest of the error handler...
    }
}</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Sorting only works once and then stops working.</p>
                    <p class="solution">Solution:</p>
                    <p>This is usually caused by the sort links not being properly updated after an AJAX request. Make sure the sort links are updated in the JavaScript:</p>
                    <pre><code>// In dynamic-table.js, in the fetchTableData success handler
if (data.sort && data.direction) {
    console.log(`Updating sort icons and links: ${data.sort} ${data.direction}`);
    $(`#dataTable-${tableId} th.sortable .sort-link`).each(function() {
        const column = $(this).data('column');
        const icon = $(this).find('i');
        const link = $(this);
        
        // Update the icon
        if (column === data.sort) {
            icon.attr('class', `fas fa-sort-${data.direction === 'asc' ? 'up' : 'down'}`);
            
            // Update the href attribute to toggle the direction next time
            const currentUrl = new URL(link.attr('href'), window.location.origin);
            const nextDirection = data.direction === 'asc' ? 'desc' : 'asc';
            currentUrl.searchParams.set('direction', nextDirection);
            link.attr('href', currentUrl.pathname + currentUrl.search);
        } else {
            icon.attr('class', 'fas fa-sort');
        }
    });
}</code></pre>
                </div>
            </div>
            
            <div class="section-card">
                <h2>PHP/Laravel Issues</h2>
                
                <div class="mb-4">
                    <p class="problem">Problem: Trait methods not found or "Method not found" errors.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the <code>HasDataTable</code> trait is properly imported in your controller</li>
                        <li>Check that the trait file is in the correct namespace</li>
                        <li>Verify that the trait methods are not being overridden incorrectly</li>
                    </ol>
                    <pre><code>namespace App\Http\Controllers;

use App\Traits\HasDataTable; // Make sure this path is correct
use Illuminate\Http\Request;

class YourController extends Controller
{
    use HasDataTable; // Use the trait
    
    // Rest of your controller...
}</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Export functionality doesn't work or returns errors.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the required packages are installed: <code>maatwebsite/excel</code> and <code>barryvdh/laravel-dompdf</code></li>
                        <li>Check that the export class is properly configured</li>
                        <li>Verify that the export class implements all required interfaces</li>
                        <li>Make sure the export class is properly registered in your controller</li>
                    </ol>
                    <pre><code>// In your controller
public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class; // Make sure this class exists
    
    // Rest of your constructor...
}</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Filter options not showing up in the filter sidebar.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the <code>getFilterOptions</code> method is properly implemented in your controller</li>
                        <li>Check that the method returns an array with the expected keys</li>
                        <li>Verify that the filter options are being passed to the view</li>
                    </ol>
                    <pre><code>protected function getFilterOptions()
{
    // Get unique categories for the filter dropdown
    $categories = YourModel::distinct('category')->pluck('category')->toArray();
    
    // Define status options
    $statuses = ['active', 'inactive', 'out_of_stock', 'pending'];
    
    return [
        'categories' => $categories,
        'statuses' => $statuses,
    ];
}</code></pre>
                </div>
            </div>
            
            <div class="section-card">
                <h2>Blade Template Issues</h2>
                
                <div class="mb-4">
                    <p class="problem">Problem: Table rows not rendering correctly or missing data.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Check that the table rows template is correctly accessing the item properties</li>
                        <li>Verify that the columns array matches the expected data structure</li>
                        <li>Make sure the item properties exist in your model</li>
                    </ol>
                    <pre><code>@foreach($items as $item)
    &lt;tr&gt;
        @foreach($columns as $key => $column)
            &lt;td&gt;
                @if(isset($columnRenderers[$key]))
                    {!! $columnRenderers[$key]($item) !!}
                @elseif(is_object($item->{$key}) && $item->{$key} instanceof \Carbon\Carbon)
                    {{ $item->{$key}->format('Y-m-d') }}
                @else
                    {{ $item->{$key} }}
                @endif
            &lt;/td&gt;
        @endforeach
        &lt;td&gt;
            &lt;!-- Actions --&gt;
        &lt;/td&gt;
    &lt;/tr&gt;
@endforeach</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Problem: Component props not being passed correctly.</p>
                    <p class="solution">Solution:</p>
                    <ol>
                        <li>Make sure the component props are properly defined in the component class</li>
                        <li>Check that the props are being passed correctly in the view</li>
                        <li>Verify that the props have the correct data types</li>
                    </ol>
                    <pre><code>// In the component class
public $items;
public $columns;
public $sortable;
public $filterOptions;
public $title;
public $routeName;

public function __construct($items, $columns, $sortable = [], $filterOptions = [], $title = 'Data Table', $routeName = 'data-table.index')
{
    $this->items = $items;
    $this->columns = $columns;
    $this->sortable = $sortable;
    $this->filterOptions = $filterOptions;
    $this->title = $title;
    $this->routeName = $routeName;
}</code></pre>
                </div>
            </div>
            
            <div class="section-card">
                <h2>Common Error Messages</h2>
                
                <div class="mb-4">
                    <p class="problem">Error: "Undefined variable: $columnRenderers"</p>
                    <p class="solution">Solution:</p>
                    <p>This error occurs when you're trying to use custom column renderers but haven't defined them in your controller or haven't passed them to the view. Make sure you define the <code>$columnRenderers</code> property in your controller and pass it to the view:</p>
                    <pre><code>// In your controller
public function __construct()
{
    // Other properties...
    
    $this->columnRenderers = [
        'price' => function($item) {
            return '$' . number_format($item->price, 2);
        },
        // Other renderers...
    ];
}

// In your handleDataTable method
return view($viewPath, [
    'items' => $items,
    'filterOptions' => $filterOptions,
    'columnRenderers' => $this->columnRenderers,
    // Other variables...
]);</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Error: "jQuery is not defined"</p>
                    <p class="solution">Solution:</p>
                    <p>This error occurs when jQuery is not loaded before the dynamic-table.js script. Make sure you include jQuery before the dynamic-table.js script:</p>
                    <pre><code>&lt;!-- In your layout file --&gt;
&lt;script src="https://code.jquery.com/jquery-3.6.0.min.js"&gt;&lt;/script&gt;
&lt;script src="{{ asset('js/dynamic-table.js') }}"&gt;&lt;/script&gt;</code></pre>
                </div>
                
                <div class="mb-4">
                    <p class="problem">Error: "Class 'App\Exports\YourExportClass' not found"</p>
                    <p class="solution">Solution:</p>
                    <p>This error occurs when the export class specified in your controller doesn't exist or is in the wrong namespace. Make sure the export class exists and is in the correct namespace:</p>
                    <pre><code>// In your controller
public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = \App\Exports\YourExportClass::class; // Use the full namespace
    
    // Rest of your constructor...
}</code></pre>
                </div>
            </div>
            
            <div class="mt-4">
                <p>If you've tried all the solutions above and are still experiencing issues, please contact support or check the GitHub repository for updates and additional troubleshooting information.</p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
