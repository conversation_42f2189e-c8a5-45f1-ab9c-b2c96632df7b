# Dynamic Table Component for Laravel

A powerful, reusable table component for Laravel applications with refresh-free operations, filtering, sorting, pagination, lazy loading, and export functionality.

## Features

- **Refresh-Free Operations**: All actions (filtering, sorting, pagination) are performed without page refresh
- **Lazy Loading**: Automatically load more data when scrolling to the bottom of the table
- **Filtering**: Comprehensive filtering options with a slide-in sidebar
- **Sorting**: Click column headers to sort data
- **Pagination**: Standard pagination with configurable items per page
- **Export**: Export data in Excel, CSV, and PDF formats
- **Responsive Design**: Works on all screen sizes
- **Highly Customizable**: Easy to adapt to any Laravel project

## Installation

### 1. Copy Required Files

Copy the following files to your Laravel project:

```
app/
├── Exports/
│   └── ProductsExport.php
├── Traits/
│   └── HasDataTable.php
├── View/
│   └── Components/
│       ├── DataTable.php
│       └── LazyTable.php
public/
├── css/
│   └── dynamic-table.css
├── js/
│   └── dynamic-table.js
resources/
└── views/
    ├── components/
    │   ├── data-table/
    │   │   ├── pagination.blade.php
    │   │   ├── table-rows.blade.php
    │   │   └── table.blade.php
    │   └── lazy-table/
    │       ├── table-rows.blade.php
    │       └── table.blade.php
    └── exports/
        └── products-pdf.blade.php
```

### 2. Install Required Packages

```bash
composer require maatwebsite/excel barryvdh/laravel-dompdf
```

### 3. Register Components

Add the following to your `app/Providers/AppServiceProvider.php`:

```php
use App\View\Components\DataTable;
use App\View\Components\LazyTable;
use Illuminate\Support\Facades\Blade;

public function boot()
{
    // Register components
    Blade::component('data-table', DataTable::class);
    Blade::component('lazy-table', LazyTable::class);
}
```

### 4. Include CSS and JavaScript

Add the following to your layout file:

```html
<!-- In your <head> section -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<link rel="stylesheet" href="{{ asset('css/dynamic-table.css') }}">

<!-- Before closing </body> tag -->
<script src="{{ asset('js/dynamic-table.js') }}"></script>
```

## Basic Usage

### 1. Create a Controller

Create a controller that uses the `HasDataTable` trait:

```php
<?php

namespace App\Http\Controllers;

use App\Exports\YourExportClass;
use App\Models\YourModel;
use App\Traits\HasDataTable;
use Illuminate\Http\Request;

class YourTableController extends Controller
{
    use HasDataTable;
    
    public function __construct()
    {
        $this->tableModel = YourModel::class;
        $this->exportClass = YourExportClass::class;
        $this->tableColumns = [
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ];
        $this->sortableColumns = ['id', 'name'];
        
        // Optional: customize filter fields
        $this->filterFields = [
            'search' => ['name', 'description'],
            'category' => 'category',
            // Add your filter fields here
        ];
    }
    
    public function index(Request $request)
    {
        return $this->handleDataTable($request, 'your-view-path');
    }
}
```

### 2. Create a Route

```php
Route::get('/your-table', [YourTableController::class, 'index'])->name('your-table.index');
```

### 3. Create a View

```blade
@extends('your-layout')

@section('content')
    <x-data-table
        :items="$items"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ]"
        :sortable="['id', 'name']"
        :filter-options="$filterOptions"
        title="Your Table Title"
        route-name="your-table.index"
    />
@endsection
```

## Lazy Loading Table

To use the lazy loading version of the table:

```php
// In your controller
public function index(Request $request)
{
    // Add lazy_load parameter to request
    $request->merge(['lazy_load' => true]);
    
    return $this->handleDataTable($request, 'your-lazy-view-path');
}
```

```blade
@extends('your-layout')

@section('content')
    <x-lazy-table
        :items="$items"
        :columns="[
            'id' => 'ID',
            'name' => 'Name',
            // Add your columns here
        ]"
        :sortable="['id', 'name']"
        :filter-options="$filterOptions"
        :total-count="$totalCount"
        :has-more-pages="$hasMorePages"
        :next-page="$nextPage"
        title="Your Lazy Loading Table"
        route-name="your-table.index"
    />
@endsection
```

## Creating an Export Class

Create a custom export class for your model:

```php
<?php

namespace App\Exports;

use App\Models\YourModel;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class YourExportClass implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    protected $request;

    public function __construct(Request $request = null)
    {
        $this->request = $request ?? request();
    }

    public function collection()
    {
        // Build your query here, similar to the controller
        $query = YourModel::query();
        
        // Apply filters based on request
        // ...
        
        return $query->get();
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            // Add your headings here
        ];
    }

    public function map($row): array
    {
        return [
            $row->id,
            $row->name,
            // Map your data here
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
```

## Customization Options

### Table Component Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `items` | Collection | Required | The data to display in the table |
| `columns` | Array | Required | The columns to display in the table |
| `sortable` | Array | `[]` | The columns that can be sorted |
| `searchable` | Boolean | `true` | Whether to enable search |
| `filterable` | Boolean | `true` | Whether to enable filters |
| `pagination` | Boolean | `true` | Whether to enable pagination |
| `lazyLoad` | Boolean | `false` | Whether to enable lazy loading |
| `exportable` | Boolean | `true` | Whether to enable export functionality |
| `exportFormats` | Array | `['excel', 'csv', 'pdf']` | Available export formats |
| `filterOptions` | Array | `[]` | Filter options for the table |
| `title` | String | `'Data Table'` | The title of the table |
| `tableClass` | String | `''` | Additional CSS classes for the table |
| `routeName` | String | `'data-table.index'` | The route name for table actions |

### Lazy Table Additional Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `totalCount` | Integer | `0` | Total number of items |
| `hasMorePages` | Boolean | `false` | Whether there are more pages to load |
| `nextPage` | Integer | `2` | The next page number to load |

## Integrating with Existing Projects

### Using with Your Own Layout

The table component can be used with any layout:

```blade
<x-data-table
    :items="$items"
    :columns="$columns"
    :sortable="$sortable"
    :filter-options="$filterOptions"
    title="Your Table"
    route-name="your-route.name"
/>
```

### Using with Custom Models

Modify the `HasDataTable` trait in your controller to work with your model:

```php
public function __construct()
{
    $this->tableModel = YourModel::class;
    $this->exportClass = YourExportClass::class;
    $this->tableColumns = [
        // Your columns
    ];
    $this->sortableColumns = [
        // Your sortable columns
    ];
    $this->filterFields = [
        // Your filter fields
    ];
}
```

### Customizing Filter Fields

You can customize the filter fields in your controller:

```php
$this->filterFields = [
    'search' => ['title', 'content'], // Fields to search
    'category' => 'category_id',      // Direct field mapping
    'status' => 'status',
    'date_from' => 'published_at',    // Date range start
    'date_to' => 'published_at',      // Date range end
];
```

## License

This component is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
