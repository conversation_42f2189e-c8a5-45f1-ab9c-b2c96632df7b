<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\Support\Str;

class LazyTable extends Component
{
    /**
     * The table ID.
     *
     * @var string
     */
    public $tableId;

    /**
     * The route name for table actions.
     *
     * @var string
     */
    public $routeName;

    /**
     * The items to display in the table.
     *
     * @var \Illuminate\Support\Collection
     */
    public $items;

    /**
     * The columns to display.
     *
     * @var array
     */
    public $columns;

    /**
     * The columns that can be sorted.
     *
     * @var array
     */
    public $sortable;

    /**
     * Whether to enable search.
     *
     * @var bool
     */
    public $searchable;

    /**
     * Whether to enable filters.
     *
     * @var bool
     */
    public $filterable;

    /**
     * Whether to enable export functionality.
     *
     * @var bool
     */
    public $exportable;

    /**
     * Available export formats.
     *
     * @var array
     */
    public $exportFormats;

    /**
     * Filter options for the table.
     *
     * @var array
     */
    public $filterOptions;

    /**
     * The title of the table.
     *
     * @var string
     */
    public $title;

    /**
     * The CSS classes to apply to the table.
     *
     * @var string
     */
    public $tableClass;

    /**
     * The parent layout to extend.
     *
     * @var string|null
     */
    public $layout;

    /**
     * Total count of items.
     *
     * @var int
     */
    public $totalCount;

    /**
     * Whether there are more pages.
     *
     * @var bool
     */
    public $hasMorePages;

    /**
     * The next page number.
     *
     * @var int
     */
    public $nextPage;

    /**
     * Create a new component instance.
     *
     * @param  mixed  $items
     * @param  array  $columns
     * @param  array  $sortable
     * @param  bool  $searchable
     * @param  bool  $filterable
     * @param  bool  $exportable
     * @param  array  $exportFormats
     * @param  array  $filterOptions
     * @param  string  $title
     * @param  string  $tableClass
     * @param  string  $routeName
     * @param  string|null  $layout
     * @param  string|null  $tableId
     * @param  int  $totalCount
     * @param  bool  $hasMorePages
     * @param  int  $nextPage
     * @return void
     */
    public function __construct(
        $items,
        array $columns,
        array $sortable = [],
        bool $searchable = true,
        bool $filterable = true,
        bool $exportable = true,
        array $exportFormats = ['excel', 'csv', 'pdf'],
        array $filterOptions = [],
        string $title = 'Lazy Loading Table',
        string $tableClass = '',
        string $routeName = 'lazy-table.index',
        ?string $layout = null,
        ?string $tableId = null,
        int $totalCount = 0,
        bool $hasMorePages = false,
        int $nextPage = 2
    ) {
        $this->items = $items;
        $this->columns = $columns;
        $this->sortable = $sortable;
        $this->searchable = $searchable;
        $this->filterable = $filterable;
        $this->exportable = $exportable;
        $this->exportFormats = $exportFormats;
        $this->filterOptions = $filterOptions;
        $this->title = $title;
        $this->tableClass = $tableClass;
        $this->routeName = $routeName;
        $this->layout = $layout;
        $this->tableId = $tableId ?? 'lazy-table-' . Str::random(8);
        $this->totalCount = $totalCount;
        $this->hasMorePages = $hasMorePages;
        $this->nextPage = $nextPage;
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|\Illuminate\Contracts\Support\Htmlable|\Closure|string
     */
    public function render()
    {
        return view('components.lazy-table.table');
    }
}
