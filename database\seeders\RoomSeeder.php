<?php

namespace Database\Seeders;

use App\Models\Room;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoomSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now(); // Get the current timestamp

        // Fake user data with timestamps
        $rooms = [
            [
                'title' => 'test 1 title',
                'slug' => 'inertia',
                'created_at' => $now,
                'updated_at' => $now,
            ],
        ];

        // Bulk insert users
        Room::insert($rooms);
    }
}
